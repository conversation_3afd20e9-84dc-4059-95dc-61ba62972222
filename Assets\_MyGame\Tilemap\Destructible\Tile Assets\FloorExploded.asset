%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9d1514134bc4fbd41bb739b1b9a49231, type: 3}
  m_Name: FloorExploded
  m_EditorClassIdentifier: 
  m_DefaultSprite: {fileID: 21300030, guid: 267d4b7f9c93d3f4696a37722d58728c, type: 3}
  m_DefaultGameObject: {fileID: 0}
  m_DefaultColliderType: 1
  m_TilingRules:
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300006, guid: 267d4b7f9c93d3f4696a37722d58728c, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0100000001000000010000000100000001000000010000000100000001000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 0
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300002, guid: 267d4b7f9c93d3f4696a37722d58728c, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0000000002000000000000000100000001000000010000000100000001000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300024, guid: 267d4b7f9c93d3f4696a37722d58728c, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0000000002000000000000000100000001000000010000000100000002000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300022, guid: 267d4b7f9c93d3f4696a37722d58728c, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0000000002000000000000000100000001000000020000000100000001000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300010, guid: 267d4b7f9c93d3f4696a37722d58728c, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0200000001000000000000000100000001000000000000000100000002000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300012, guid: 267d4b7f9c93d3f4696a37722d58728c, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0200000001000000000000000100000001000000020000000100000002000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300014, guid: 267d4b7f9c93d3f4696a37722d58728c, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0200000001000000000000000100000001000000020000000100000000000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300016, guid: 267d4b7f9c93d3f4696a37722d58728c, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0200000001000000020000000100000001000000020000000100000002000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300020, guid: 267d4b7f9c93d3f4696a37722d58728c, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0000000002000000000000000100000001000000020000000100000002000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300002, guid: 267d4b7f9c93d3f4696a37722d58728c, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0000000002000000000000000100000001000000010000000100000001000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300004, guid: 267d4b7f9c93d3f4696a37722d58728c, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0000000001000000000000000100000001000000000000000100000002000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300018, guid: 267d4b7f9c93d3f4696a37722d58728c, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0000000002000000000000000200000001000000000000000100000002000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300026, guid: 267d4b7f9c93d3f4696a37722d58728c, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0000000002000000000000000200000002000000000000000100000000000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300008, guid: 267d4b7f9c93d3f4696a37722d58728c, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0000000002000000000000000100000001000000000000000200000000000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300028, guid: 267d4b7f9c93d3f4696a37722d58728c, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0000000002000000000000000200000002000000000000000200000000000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 0
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300000, guid: 267d4b7f9c93d3f4696a37722d58728c, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0000000002000000000000000200000001000000000000000100000001000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
