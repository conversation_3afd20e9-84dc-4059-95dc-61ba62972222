%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0, g: 0, b: 0, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 0
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &29514215
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 29514219}
  - component: {fileID: 29514218}
  - component: {fileID: 29514216}
  - component: {fileID: 29514220}
  - component: {fileID: 29514221}
  m_Layer: 0
  m_Name: Brick
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!50 &29514216
Rigidbody2D:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 29514215}
  m_BodyType: 0
  m_Simulated: 1
  m_UseFullKinematicContacts: 0
  m_UseAutoMass: 0
  m_Mass: 1
  m_LinearDrag: 0
  m_AngularDrag: 0
  m_GravityScale: 0
  m_Material: {fileID: 0}
  m_Interpolate: 0
  m_SleepingMode: 1
  m_CollisionDetection: 0
  m_Constraints: 6
--- !u!212 &29514218
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 29514215}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: eff080ca3ace8a04d895f3147e146c55, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.64, y: 0.64}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!4 &29514219
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 29514215}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 8, y: 0.25, z: 0}
  m_LocalScale: {x: 3, y: 0.5, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &29514220
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 29514215}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 338e0cd0e16443c4caee0c10604195e6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  moveSpeed: 5
--- !u!60 &29514221
PolygonCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 29514215}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0.5, y: 0.5}
    oldSize: {x: 1, y: 1}
    newSize: {x: 0.64, y: 0.64}
    adaptiveTilingThreshold: 0.5
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  m_Points:
    m_Paths:
    - - {x: -0.5, y: -0.5}
      - {x: -0.5, y: 0.47}
      - {x: 0, y: 0.54}
      - {x: 0.5, y: 0.47}
      - {x: 0.5, y: -0.5}
--- !u!1 &154894189
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 154894190}
  - component: {fileID: 154894195}
  - component: {fileID: 154894194}
  - component: {fileID: 154894193}
  - component: {fileID: 154894192}
  - component: {fileID: 154894191}
  m_Layer: 0
  m_Name: Tilemap
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &154894190
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 154894189}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 8, z: 0}
  m_LocalScale: {x: 1, y: 0.5, z: 1}
  m_Children: []
  m_Father: {fileID: 420221129}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!66 &154894191
CompositeCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 154894189}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_GeometryType: 0
  m_GenerationType: 0
  m_EdgeRadius: 0
  m_ColliderPaths: []
  m_CompositePaths:
    m_Paths: []
  m_VertexDistance: 0.0005
  m_OffsetDistance: 0.00005
--- !u!50 &154894192
Rigidbody2D:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 154894189}
  m_BodyType: 2
  m_Simulated: 1
  m_UseFullKinematicContacts: 0
  m_UseAutoMass: 0
  m_Mass: 1
  m_LinearDrag: 0
  m_AngularDrag: 0.05
  m_GravityScale: 1
  m_Material: {fileID: 0}
  m_Interpolate: 0
  m_SleepingMode: 1
  m_CollisionDetection: 0
  m_Constraints: 0
--- !u!19719996 &154894193
TilemapCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 154894189}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_MaximumTileChangeCount: 1000
  m_ExtrusionFactor: 0.00001
--- !u!483693784 &154894194
TilemapRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 154894189}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_ChunkSize: {x: 32, y: 32, z: 32}
  m_ChunkCullingBounds: {x: 0, y: 0, z: 0}
  m_MaxChunkCount: 16
  m_MaxFrameAge: 16
  m_SortOrder: 0
  m_Mode: 0
  m_DetectChunkCullingBounds: 0
  m_MaskInteraction: 0
--- !u!1839735485 &154894195
Tilemap:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 154894189}
  m_Enabled: 1
  m_Tiles:
  - first: {x: 0, y: 8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: 8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: 8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: 8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 8, y: 8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 9, y: 8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 10, y: 8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 11, y: 8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 12, y: 8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 13, y: 8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 14, y: 8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 15, y: 8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: 9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: 9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: 9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 8, y: 9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 9, y: 9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 10, y: 9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 11, y: 9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 12, y: 9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 13, y: 9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 14, y: 9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 15, y: 9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: 10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: 10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: 10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 8, y: 10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 9, y: 10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 10, y: 10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 11, y: 10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 12, y: 10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 13, y: 10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 14, y: 10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 15, y: 10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: 11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: 11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: 11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 8, y: 11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 9, y: 11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 10, y: 11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 11, y: 11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 12, y: 11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 13, y: 11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 14, y: 11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 15, y: 11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 1
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 12, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 12, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 12, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 12, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: 12, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: 12, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: 12, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 12, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 8, y: 12, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 9, y: 12, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 10, y: 12, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 11, y: 12, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 12, y: 12, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 13, y: 12, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 14, y: 12, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 15, y: 12, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 13, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 13, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 13, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 13, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: 13, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: 13, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: 13, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 13, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 8, y: 13, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 9, y: 13, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 10, y: 13, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 11, y: 13, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 12, y: 13, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 13, y: 13, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 14, y: 13, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 15, y: 13, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 2
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 14, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 14, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 14, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 14, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: 14, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: 14, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: 14, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 14, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 8, y: 14, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 9, y: 14, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 10, y: 14, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 11, y: 14, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 12, y: 14, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 13, y: 14, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 14, y: 14, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 15, y: 14, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 15, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 15, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 15, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 15, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: 15, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: 15, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: 15, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 15, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 8, y: 15, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 9, y: 15, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 10, y: 15, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 11, y: 15, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 12, y: 15, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 13, y: 15, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 14, y: 15, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 15, y: 15, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 3
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  m_AnimatedTiles: {}
  m_TileAssetArray:
  - m_RefCount: 32
    m_Data: {fileID: 11400000, guid: 15d5f3da2fadd15468ecab880beef846, type: 2}
  - m_RefCount: 32
    m_Data: {fileID: 11400000, guid: 8dfcc408430a7514783efdda828875a0, type: 2}
  - m_RefCount: 32
    m_Data: {fileID: 11400000, guid: db71319be18625e41b669442e5e7f861, type: 2}
  - m_RefCount: 32
    m_Data: {fileID: 11400000, guid: 898ab856ab374ef428ab13e10c948a0d, type: 2}
  m_TileSpriteArray:
  - m_RefCount: 128
    m_Data: {fileID: 21300000, guid: 0a451cb9697c39044815bbd2d048826c, type: 3}
  m_TileMatrixArray:
  - m_RefCount: 128
    m_Data:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  m_TileColorArray:
  - m_RefCount: 32
    m_Data: {r: 0.034482475, g: 0, b: 1, a: 1}
  - m_RefCount: 32
    m_Data: {r: 0, g: 1, b: 0.17241383, a: 1}
  - m_RefCount: 32
    m_Data: {r: 1, g: 0, b: 0, a: 1}
  - m_RefCount: 32
    m_Data: {r: 0.94482756, g: 1, b: 0, a: 1}
  m_TileObjectToInstantiateArray: []
  m_AnimationFrameRate: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Origin: {x: 0, y: 0, z: 0}
  m_Size: {x: 16, y: 16, z: 1}
  m_TileAnchor: {x: 0.5, y: 0.5, z: 0}
  m_TileOrientation: 0
  m_TileOrientationMatrix:
    e00: 1
    e01: 0
    e02: 0
    e03: 0
    e10: 0
    e11: 1
    e12: 0
    e13: 0
    e20: 0
    e21: 0
    e22: 1
    e23: 0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
--- !u!1 &420221126
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 420221129}
  - component: {fileID: 420221128}
  - component: {fileID: 420221127}
  - component: {fileID: 420221131}
  - component: {fileID: 420221130}
  m_Layer: 0
  m_Name: Grid
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!61 &420221127
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 420221126}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: -0.5, y: 7}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0, y: 0}
    oldSize: {x: 0, y: 0}
    newSize: {x: 0, y: 0}
    adaptiveTilingThreshold: 0
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  serializedVersion: 2
  m_Size: {x: 1, y: 20}
  m_EdgeRadius: 0
--- !u!156049354 &420221128
Grid:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 420221126}
  m_Enabled: 1
  m_CellSize: {x: 1, y: 1, z: 0}
  m_CellGap: {x: 0, y: 0, z: 0}
  m_CellLayout: 0
  m_CellSwizzle: 0
--- !u!4 &420221129
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 420221126}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 154894190}
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!61 &420221130
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 420221126}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 8, y: 17.5}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0, y: 0}
    oldSize: {x: 0, y: 0}
    newSize: {x: 0, y: 0}
    adaptiveTilingThreshold: 0
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  serializedVersion: 2
  m_Size: {x: 18, y: 1}
  m_EdgeRadius: 0
--- !u!61 &420221131
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 420221126}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 16.5, y: 7}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0, y: 0}
    oldSize: {x: 0, y: 0}
    newSize: {x: 0, y: 0}
    adaptiveTilingThreshold: 0
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  serializedVersion: 2
  m_Size: {x: 1, y: 20}
  m_EdgeRadius: 0
--- !u!1 &602162720
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 602162724}
  - component: {fileID: 602162723}
  - component: {fileID: 602162722}
  - component: {fileID: 602162721}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &602162721
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 602162720}
  m_Enabled: 1
--- !u!124 &602162722
Behaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 602162720}
  m_Enabled: 1
--- !u!20 &602162723
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 602162720}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 8
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &602162724
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 602162720}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 8, y: 8, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &840740616
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 840740620}
  - component: {fileID: 840740619}
  - component: {fileID: 840740618}
  - component: {fileID: 840740621}
  - component: {fileID: 840740617}
  m_Layer: 0
  m_Name: Ball
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!61 &840740617
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 840740616}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 6200000, guid: aad135f58881d914b93376aa42b7114f, type: 2}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0.5, y: 0.5}
    oldSize: {x: 1, y: 1}
    newSize: {x: 0.64, y: 0.64}
    adaptiveTilingThreshold: 0.5
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  serializedVersion: 2
  m_Size: {x: 1, y: 1}
  m_EdgeRadius: 0
--- !u!50 &840740618
Rigidbody2D:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 840740616}
  m_BodyType: 0
  m_Simulated: 1
  m_UseFullKinematicContacts: 0
  m_UseAutoMass: 0
  m_Mass: 0.25
  m_LinearDrag: 0
  m_AngularDrag: 0
  m_GravityScale: 0
  m_Material: {fileID: 0}
  m_Interpolate: 0
  m_SleepingMode: 1
  m_CollisionDetection: 0
  m_Constraints: 4
--- !u!212 &840740619
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 840740616}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: eff080ca3ace8a04d895f3147e146c55, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.64, y: 0.64}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!4 &840740620
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 840740616}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 8, y: 10, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &840740621
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 840740616}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a58db1f2f21de404791fca46f608ac7e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialVelocity: {x: 2, y: 15}
  tilemapGameObject: {fileID: 154894189}
--- !u!1 &1589236689
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1589236691}
  - component: {fileID: 1589236690}
  - component: {fileID: 1589236692}
  m_Layer: 0
  m_Name: Game End
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!61 &1589236690
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1589236689}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 1
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0, y: 0}
    oldSize: {x: 0, y: 0}
    newSize: {x: 0, y: 0}
    adaptiveTilingThreshold: 0
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  serializedVersion: 2
  m_Size: {x: 32, y: 6}
  m_EdgeRadius: 0
--- !u!4 &1589236691
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1589236689}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 8, y: -6, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1589236692
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1589236689}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8f193a53198432247a3c79e70873e2a2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
