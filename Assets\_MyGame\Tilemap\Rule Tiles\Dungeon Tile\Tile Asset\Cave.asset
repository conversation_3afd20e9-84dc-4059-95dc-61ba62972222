%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9d1514134bc4fbd41bb739b1b9a49231, type: 3}
  m_Name: Cave
  m_EditorClassIdentifier: 
  m_DefaultSprite: {fileID: 21300032, guid: 5c4f1fa01d076b0448a9438531d30a91, type: 3}
  m_DefaultGameObject: {fileID: 0}
  m_DefaultColliderType: 1
  m_TilingRules:
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300034, guid: 5c4f1fa01d076b0448a9438531d30a91, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 01000000020000000200000002000000
    m_NeighborPositions:
    - {x: 0, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300036, guid: 5c4f1fa01d076b0448a9438531d30a91, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0100000002000000020000000100000002000000
    m_NeighborPositions:
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300038, guid: 5c4f1fa01d076b0448a9438531d30a91, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0100000001000000020000000100000002000000
    m_NeighborPositions:
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300040, guid: 5c4f1fa01d076b0448a9438531d30a91, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 01000000020000000200000001000000
    m_NeighborPositions:
    - {x: 0, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300042, guid: 5c4f1fa01d076b0448a9438531d30a91, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 010000000200000002000000010000000100000002000000
    m_NeighborPositions:
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300044, guid: 5c4f1fa01d076b0448a9438531d30a91, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0100000002000000010000000100000002000000
    m_NeighborPositions:
    - {x: 0, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300046, guid: 5c4f1fa01d076b0448a9438531d30a91, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0100000002000000020000000100000001000000
    m_NeighborPositions:
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300048, guid: 5c4f1fa01d076b0448a9438531d30a91, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 010000000100000002000000010000000100000001000000
    m_NeighborPositions:
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300050, guid: 5c4f1fa01d076b0448a9438531d30a91, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0200000001000000020000000100000001000000020000000100000002000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 0
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300052, guid: 5c4f1fa01d076b0448a9438531d30a91, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0200000001000000010000000100000001000000020000000100000002000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300054, guid: 5c4f1fa01d076b0448a9438531d30a91, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0200000001000000010000000100000001000000020000000100000001000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300056, guid: 5c4f1fa01d076b0448a9438531d30a91, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0200000001000000010000000100000001000000010000000100000002000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300058, guid: 5c4f1fa01d076b0448a9438531d30a91, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0200000001000000010000000100000001000000010000000100000001000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 1
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300060, guid: 5c4f1fa01d076b0448a9438531d30a91, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.915
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0100000001000000010000000100000001000000010000000100000001000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: 1, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 0
