Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.14f1 (eee1884e7226) revision 15655304'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 32577 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files\Unity\2021.3.14f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
E:/ProjectsUnity/City
-logFile
Logs/AssetImportWorker2.log
-srvPort
55291
Successfully changed project path to: E:/ProjectsUnity/City
E:/ProjectsUnity/City
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Refreshing native plugins compatible for Editor in 66.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.14f1 (eee1884e7226)
[Subsystems] Discovering subsystems at path D:/Program Files/Unity/2021.3.14f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/ProjectsUnity/City/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 Ti (ID=0x2803)
    Vendor:   NVIDIA
    VRAM:     7949 MB
    Driver:   32.0.15.6117
Initialize mono
Mono path[0] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files/Unity/2021.3.14f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56260
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files/Unity/2021.3.14f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001234 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 71.44 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.404 seconds
Domain Reload Profiling:
	ReloadAssembly (404ms)
		BeginReloadAssembly (39ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (309ms)
			LoadAssemblies (38ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (61ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (20ms)
			SetupLoadedEditorAssemblies (198ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (37ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (71ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (62ms)
				ProcessInitializeOnLoadMethodAttributes (27ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.002372 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 68.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.960 seconds
Domain Reload Profiling:
	ReloadAssembly (961ms)
		BeginReloadAssembly (188ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (138ms)
		EndReloadAssembly (710ms)
			LoadAssemblies (56ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (195ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (44ms)
			SetupLoadedEditorAssemblies (382ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (8ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (69ms)
				BeforeProcessingInitializeOnLoad (68ms)
				ProcessInitializeOnLoadAttributes (168ms)
				ProcessInitializeOnLoadMethodAttributes (68ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (4ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 0.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5726 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (1.2 MB). Loaded Objects now: 6166.
Memory consumption went from 221.0 MB to 219.8 MB.
Total: 2.356100 ms (FindLiveObjects: 0.230100 ms CreateObjectMapping: 0.083100 ms MarkObjects: 1.755000 ms  DeleteObjects: 0.287400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 18130.233404 seconds.
  path: Assets/_MyGame/Tilemap/Destructible/Sprites/floorexploded.png
  artifactKey: Guid(267d4b7f9c93d3f4696a37722d58728c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Destructible/Sprites/floorexploded.png using Guid(267d4b7f9c93d3f4696a37722d58728c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cfecf7ad62303efb29ff1ea0d14b4aa7') in 0.071902 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.825387 seconds.
  path: Assets/_MyGame/Tilemap/Destructible/Sprites/destruct.PNG
  artifactKey: Guid(6effb0022735ead45acf361ef8832074) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Destructible/Sprites/destruct.PNG using Guid(6effb0022735ead45acf361ef8832074) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'af0cd4dc44dea69ce742c9524bf89bc2') in 0.042680 seconds 
========================================================================
Received Import Request.
  Time since last request: 8.565058 seconds.
  path: Assets/_MyGame/Tilemap/Destructible/Sprites
  artifactKey: Guid(defc6f2683f5fe3478ad91ed4a72c2b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Destructible/Sprites using Guid(defc6f2683f5fe3478ad91ed4a72c2b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3ebb621e512541e120753154dd889115') in 0.000935 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.068715 seconds.
  path: Assets/_MyGame/Tilemap/Destructible/Sprites/explosion.png
  artifactKey: Guid(195d2ebbbf668124ab3fa9932a849bcb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Destructible/Sprites/explosion.png using Guid(195d2ebbbf668124ab3fa9932a849bcb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2630ff5b2ea6c3324b8e32acbb9ded59') in 0.010312 seconds 
========================================================================
Received Import Request.
  Time since last request: 18.203533 seconds.
  path: Assets/_MyGame/Tilemap/Destructible/Explosion/Explosion.prefab
  artifactKey: Guid(30b58b96590ae7d478cf1e262ecb7e10) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Destructible/Explosion/Explosion.prefab using Guid(30b58b96590ae7d478cf1e262ecb7e10) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3a1bcccb5c17c156ae875e38deb122dd') in 0.009277 seconds 
========================================================================
Received Import Request.
  Time since last request: 5.400155 seconds.
  path: Assets/_MyGame/Tilemap/Brick/Physics2D/Brick.physicsMaterial2D
  artifactKey: Guid(aad135f58881d914b93376aa42b7114f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Brick/Physics2D/Brick.physicsMaterial2D using Guid(aad135f58881d914b93376aa42b7114f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a4235ac513657b5996c8dc808b0a5e99') in 0.002842 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.286353 seconds.
  path: Assets/_MyGame/Tilemap/Brick/Sprites/Ball.png
  artifactKey: Guid(eff080ca3ace8a04d895f3147e146c55) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Brick/Sprites/Ball.png using Guid(eff080ca3ace8a04d895f3147e146c55) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd9fbb680caeee4d245f94a12c6e065db') in 0.006455 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.795741 seconds.
  path: Assets/_MyGame/Tilemap/Brick/TileAssets/BlueBrick.asset
  artifactKey: Guid(15d5f3da2fadd15468ecab880beef846) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Brick/TileAssets/BlueBrick.asset using Guid(15d5f3da2fadd15468ecab880beef846) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '83baf318fbd63b78b4db99af0a8571e8') in 0.007828 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.626200 seconds.
  path: Assets/_MyGame/Tilemap/Brick/TilePalettes/Blocks.prefab
  artifactKey: Guid(13f3e9d7e29a1714195fb0ee95019fcd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Brick/TilePalettes/Blocks.prefab using Guid(13f3e9d7e29a1714195fb0ee95019fcd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ef6be9009cd20a300c3c5e254c5ac6fa') in 0.088875 seconds 
========================================================================
Received Import Request.
  Time since last request: 11.876536 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Animated Tile/Animated Tile.unity
  artifactKey: Guid(3c0087e4ddaa8674c955f2e8946dcab8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Tiles/Animated Tile/Animated Tile.unity using Guid(3c0087e4ddaa8674c955f2e8946dcab8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fdae8a22ed6bf39b62c655cf5eda8520') in 0.001368 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002162 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\ProjectsUnity\City\Library\PackageCache\com.unity.visualscripting@1.7.8\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.54 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.672 seconds
Domain Reload Profiling:
	ReloadAssembly (673ms)
		BeginReloadAssembly (78ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (541ms)
			LoadAssemblies (42ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (157ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (251ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (49ms)
				ProcessInitializeOnLoadAttributes (139ms)
				ProcessInitializeOnLoadMethodAttributes (49ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5615 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (1.2 MB). Loaded Objects now: 6230.
Memory consumption went from 220.6 MB to 219.4 MB.
Total: 2.363000 ms (FindLiveObjects: 0.221700 ms CreateObjectMapping: 0.081900 ms MarkObjects: 1.754800 ms  DeleteObjects: 0.303900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 68.433874 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Animated Tile/Sprite Atlas
  artifactKey: Guid(97ae7dc776a3cac4a8cd8f1b8a0bfd0f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Tiles/Animated Tile/Sprite Atlas using Guid(97ae7dc776a3cac4a8cd8f1b8a0bfd0f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3da855c3150447c4bf1d2c030c77da6d') in 0.004834 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.053128 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Animated Tile/Sprite Atlas/Animated Tiles.spriteatlas
  artifactKey: Guid(7015aac6848b4484eb19bc917e2f066e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Tiles/Animated Tile/Sprite Atlas/Animated Tiles.spriteatlas using Guid(7015aac6848b4484eb19bc917e2f066e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '129eeea1df506d4cc2761a73724b76df') in 0.011524 seconds 
========================================================================
Received Import Request.
  Time since last request: 7.182830 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Animated Tile/Sprites/Animated Tiles.png
  artifactKey: Guid(bf541ba7810cafd43af665d35d1dc29e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Tiles/Animated Tile/Sprites/Animated Tiles.png using Guid(bf541ba7810cafd43af665d35d1dc29e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6ee0353808e2b1076a099ccd9b9fee8b') in 0.037013 seconds 
========================================================================
Received Import Request.
  Time since last request: 34.363576 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Animated Tile/Tile Asset/WaterfallMain.asset
  artifactKey: Guid(f3abecf6a5b1baa46a729a298ae473b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Tiles/Animated Tile/Tile Asset/WaterfallMain.asset using Guid(f3abecf6a5b1baa46a729a298ae473b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3bcad95daab99a24a959bb9db94745b6') in 0.002318 seconds 
========================================================================
Received Import Request.
  Time since last request: 17.670062 seconds.
  path: Assets/_MyGame/Tilemap/Tiles/Animated Tile/Tile Palette/Animated Tiles.prefab
  artifactKey: Guid(e48163b530eeebc49b82a236bd09123c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MyGame/Tilemap/Tiles/Animated Tile/Tile Palette/Animated Tiles.prefab using Guid(e48163b530eeebc49b82a236bd09123c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7956273deb37a233e8385b43f8bd3ddb') in 0.085780 seconds 
