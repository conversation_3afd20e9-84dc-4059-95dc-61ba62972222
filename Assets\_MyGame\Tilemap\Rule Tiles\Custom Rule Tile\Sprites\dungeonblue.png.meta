fileFormatVersion: 2
guid: 1323b38fd2229a441a036c00a0c74fa7
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 1581162302303655051
    second: dungeonblue_0
  - first:
      213: -5237374960337123953
    second: dungeonblue_1
  - first:
      213: -7740534794218406516
    second: dungeonblue_2
  - first:
      213: 8895147284628354134
    second: dungeonblue_3
  - first:
      213: 1894414775277152233
    second: dungeonblue_4
  - first:
      213: -2293585462942885448
    second: dungeonblue_5
  - first:
      213: 1836671909477135375
    second: dungeonblue_6
  - first:
      213: -6081648952187890055
    second: dungeonblue_7
  - first:
      213: 2193444227186103523
    second: dungeonblue_8
  - first:
      213: 2578947068420565638
    second: dungeonblue_9
  - first:
      213: -9111311350863034959
    second: dungeonblue_10
  - first:
      213: 3823663072149439348
    second: dungeonblue_11
  - first:
      213: -6253547596991593336
    second: dungeonblue_12
  - first:
      213: 2487789472100934703
    second: dungeonblue_13
  - first:
      213: 8490591127244828120
    second: dungeonblue_14
  - first:
      213: -964911204903380505
    second: dungeonblue_15
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: dungeonblue_0
      rect:
        serializedVersion: 2
        x: 0
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b845c019cba61f510800000000000000
      internalID: 1581162302303655051
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dungeonblue_1
      rect:
        serializedVersion: 2
        x: 16
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f85849de83b1157b0800000000000000
      internalID: -5237374960337123953
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dungeonblue_2
      rect:
        serializedVersion: 2
        x: 32
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c8d7f03ad98149490800000000000000
      internalID: -7740534794218406516
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dungeonblue_3
      rect:
        serializedVersion: 2
        x: 48
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 654bd0da359e17b70800000000000000
      internalID: 8895147284628354134
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dungeonblue_4
      rect:
        serializedVersion: 2
        x: 0
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9e765a973305a4a10800000000000000
      internalID: 1894414775277152233
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dungeonblue_5
      rect:
        serializedVersion: 2
        x: 16
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8b91a436c2c8b20e0800000000000000
      internalID: -2293585462942885448
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dungeonblue_6
      rect:
        serializedVersion: 2
        x: 32
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f04b25dde5b2d7910800000000000000
      internalID: 1836671909477135375
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dungeonblue_7
      rect:
        serializedVersion: 2
        x: 48
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 976d63cff74a99ba0800000000000000
      internalID: -6081648952187890055
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dungeonblue_8
      rect:
        serializedVersion: 2
        x: 0
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3e41f0c37eda07e10800000000000000
      internalID: 2193444227186103523
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dungeonblue_9
      rect:
        serializedVersion: 2
        x: 16
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 68a2eb3e1b24ac320800000000000000
      internalID: 2578947068420565638
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dungeonblue_10
      rect:
        serializedVersion: 2
        x: 32
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1b503d5a99e1e8180800000000000000
      internalID: -9111311350863034959
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dungeonblue_11
      rect:
        serializedVersion: 2
        x: 48
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4730f343e41601530800000000000000
      internalID: 3823663072149439348
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dungeonblue_12
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 884815fd79fe639a0800000000000000
      internalID: -6253547596991593336
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dungeonblue_13
      rect:
        serializedVersion: 2
        x: 16
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f2468801857668220800000000000000
      internalID: 2487789472100934703
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dungeonblue_14
      rect:
        serializedVersion: 2
        x: 32
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8dde28825a3a4d570800000000000000
      internalID: 8490591127244828120
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dungeonblue_15
      rect:
        serializedVersion: 2
        x: 48
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7e990f71f52fb92f0800000000000000
      internalID: -964911204903380505
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      dungeonblue_14: 8490591127244828120
      dungeonblue_2: -7740534794218406516
      dungeonblue_9: 2578947068420565638
      dungeonblue_0: 1581162302303655051
      dungeonblue_13: 2487789472100934703
      dungeonblue_12: -6253547596991593336
      dungeonblue_5: -2293585462942885448
      dungeonblue_15: -964911204903380505
      dungeonblue_4: 1894414775277152233
      dungeonblue_10: -9111311350863034959
      dungeonblue_1: -5237374960337123953
      dungeonblue_7: -6081648952187890055
      dungeonblue_3: 8895147284628354134
      dungeonblue_8: 2193444227186103523
      dungeonblue_6: 1836671909477135375
      dungeonblue_11: 3823663072149439348
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
