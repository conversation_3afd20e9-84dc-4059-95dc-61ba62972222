%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0, g: 0, b: 0, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 0
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &434284536
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 434284540}
  - component: {fileID: 434284539}
  - component: {fileID: 434284538}
  - component: {fileID: 434284537}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &434284537
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 434284536}
  m_Enabled: 1
--- !u!124 &434284538
Behaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 434284536}
  m_Enabled: 1
--- !u!20 &434284539
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 434284536}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &434284540
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 434284536}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1212911233
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1212911234}
  - component: {fileID: 1212911236}
  - component: {fileID: 1212911235}
  m_Layer: 0
  m_Name: Tilemap
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1212911234
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1212911233}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2115819479}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!483693784 &1212911235
TilemapRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1212911233}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_ChunkSize: {x: 32, y: 32, z: 32}
  m_ChunkCullingBounds: {x: 0, y: 0, z: 0}
  m_MaxChunkCount: 16
  m_MaxFrameAge: 16
  m_SortOrder: 0
  m_Mode: 0
  m_DetectChunkCullingBounds: 0
  m_MaskInteraction: 0
--- !u!1839735485 &1212911236
Tilemap:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1212911233}
  m_Enabled: 1
  m_Tiles:
  - first: {x: -4, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 6
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 14
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 6
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 15
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 4
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 1
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 6
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 14
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 6
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 16
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 4
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 7
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 16
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 1
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 1
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 12
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 6
      m_TileMatrixIndex: 1
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 15
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 11
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 13
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 1
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 4
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 1
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 6
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 6
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 6
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 1
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 3
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 6
      m_TileMatrixIndex: 1
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 4
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 2
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 4
      m_TileMatrixIndex: 1
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  m_AnimatedTiles: {}
  m_TileAssetArray:
  - m_RefCount: 22
    m_Data: {fileID: 11400000, guid: 84f5a8cb641d46b4da008e78abe50772, type: 2}
  - m_RefCount: 42
    m_Data: {fileID: 11400000, guid: b28b928dff2a98f4d8b8c64582e76624, type: 2}
  m_TileSpriteArray:
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 1
    m_Data: {fileID: 21300002, guid: a51e743cd78e513489bb4b23cd3d13f2, type: 3}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 7
    m_Data: {fileID: 21300062, guid: a51e743cd78e513489bb4b23cd3d13f2, type: 3}
  - m_RefCount: 5
    m_Data: {fileID: 21300004, guid: a51e743cd78e513489bb4b23cd3d13f2, type: 3}
  - m_RefCount: 18
    m_Data: {fileID: 21300008, guid: a51e743cd78e513489bb4b23cd3d13f2, type: 3}
  - m_RefCount: 9
    m_Data: {fileID: 21300058, guid: a51e743cd78e513489bb4b23cd3d13f2, type: 3}
  - m_RefCount: 1
    m_Data: {fileID: 21300070, guid: a51e743cd78e513489bb4b23cd3d13f2, type: 3}
  - m_RefCount: 4
    m_Data: {fileID: 21300016, guid: a51e743cd78e513489bb4b23cd3d13f2, type: 3}
  - m_RefCount: 7
    m_Data: {fileID: 21300012, guid: a51e743cd78e513489bb4b23cd3d13f2, type: 3}
  - m_RefCount: 3
    m_Data: {fileID: 21300010, guid: a51e743cd78e513489bb4b23cd3d13f2, type: 3}
  - m_RefCount: 1
    m_Data: {fileID: 21300024, guid: a51e743cd78e513489bb4b23cd3d13f2, type: 3}
  - m_RefCount: 1
    m_Data: {fileID: 21300056, guid: a51e743cd78e513489bb4b23cd3d13f2, type: 3}
  - m_RefCount: 1
    m_Data: {fileID: 21300014, guid: a51e743cd78e513489bb4b23cd3d13f2, type: 3}
  - m_RefCount: 2
    m_Data: {fileID: 21300064, guid: a51e743cd78e513489bb4b23cd3d13f2, type: 3}
  - m_RefCount: 2
    m_Data: {fileID: 21300006, guid: a51e743cd78e513489bb4b23cd3d13f2, type: 3}
  - m_RefCount: 2
    m_Data: {fileID: 21300072, guid: a51e743cd78e513489bb4b23cd3d13f2, type: 3}
  m_TileMatrixArray:
  - m_RefCount: 12
    m_Data:
      e00: 0.000000059604645
      e01: -0.99999994
      e02: -0
      e03: 0
      e10: 0.99999994
      e11: 0.000000059604645
      e12: 0
      e13: 0
      e20: 0
      e21: -0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - m_RefCount: 9
    m_Data:
      e00: -1
      e01: -0.00000008742278
      e02: -0
      e03: 0
      e10: 0.00000008742278
      e11: -1
      e12: 0
      e13: 0
      e20: 0
      e21: -0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - m_RefCount: 25
    m_Data:
      e00: 0.000000059604645
      e01: 0.99999994
      e02: 0
      e03: 0
      e10: -0.99999994
      e11: 0.000000059604645
      e12: -0
      e13: 0
      e20: -0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - m_RefCount: 18
    m_Data:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  m_TileColorArray:
  - m_RefCount: 64
    m_Data: {r: 1, g: 1, b: 1, a: 1}
  m_TileObjectToInstantiateArray: []
  m_AnimationFrameRate: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Origin: {x: -4, y: -4, z: 0}
  m_Size: {x: 8, y: 8, z: 1}
  m_TileAnchor: {x: 0.5, y: 0.5, z: 0}
  m_TileOrientation: 0
  m_TileOrientationMatrix:
    e00: 1
    e01: 0
    e02: 0
    e03: 0
    e10: 0
    e11: 1
    e12: 0
    e13: 0
    e20: 0
    e21: 0
    e22: 1
    e23: 0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
--- !u!1 &2115819477
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2115819479}
  - component: {fileID: 2115819478}
  m_Layer: 0
  m_Name: Grid
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!156049354 &2115819478
Grid:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2115819477}
  m_Enabled: 1
  m_CellSize: {x: 1, y: 1, z: 0}
  m_CellGap: {x: 0, y: 0, z: 0}
  m_CellLayout: 0
  m_CellSwizzle: 0
--- !u!4 &2115819479
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2115819477}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1212911234}
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
