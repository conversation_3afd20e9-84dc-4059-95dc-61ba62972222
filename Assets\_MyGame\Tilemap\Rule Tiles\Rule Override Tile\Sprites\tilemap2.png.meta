fileFormatVersion: 2
guid: 2eff0de609d46d042bb46c56ac688c42
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 6530179738444782934
    second: sheet_0
  - first:
      213: 613435604629981061
    second: sheet_1
  - first:
      213: -3656531271222932061
    second: sheet_2
  - first:
      213: 3930011312167392660
    second: sheet_3
  - first:
      213: -96796154962138083
    second: sheet_4
  - first:
      213: -7054820123952135176
    second: sheet_5
  - first:
      213: 4530623484691778272
    second: sheet_6
  - first:
      213: -5581956015464806101
    second: sheet_7
  - first:
      213: -2661919970533589962
    second: sheet_8
  - first:
      213: 3038985000587531000
    second: sheet_9
  - first:
      213: 3216594195550930655
    second: sheet_10
  - first:
      213: -3701851516310602662
    second: sheet_11
  - first:
      213: 5893540339403691780
    second: sheet_12
  - first:
      213: -1254599994303000284
    second: sheet_13
  - first:
      213: 1949730731670058941
    second: sheet_14
  - first:
      213: -3627854407871609761
    second: sheet_15
  - first:
      213: -1888837539432032468
    second: sheet_16
  - first:
      213: -1796502737815246115
    second: sheet_17
  - first:
      213: 8636805145482191278
    second: sheet_18
  - first:
      213: -2218698658706304759
    second: sheet_19
  - first:
      213: 2562169664635839239
    second: sheet_20
  - first:
      213: -4946619765975422720
    second: sheet_21
  - first:
      213: 2281915828476810391
    second: sheet_22
  - first:
      213: -1460219255197261928
    second: sheet_23
  - first:
      213: -272350928928935694
    second: sheet_24
  - first:
      213: 3647273195039021549
    second: sheet_25
  - first:
      213: 3504542958272820299
    second: sheet_26
  - first:
      213: 6202079318447832090
    second: sheet_27
  - first:
      213: 6217686846738184359
    second: sheet_28
  - first:
      213: -3807297515833748856
    second: sheet_29
  - first:
      213: 4372381551759688375
    second: sheet_30
  - first:
      213: -8302254863492273045
    second: sheet_31
  - first:
      213: -863169582964885348
    second: sheet_32
  - first:
      213: -832198905909771304
    second: sheet_33
  - first:
      213: 662046916731068474
    second: sheet_34
  - first:
      213: -5679681372999991991
    second: sheet_35
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 70
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: sheet_0
      rect:
        serializedVersion: 2
        x: 0
        y: 442
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2342afaaa00252d4299091fc922942be
      internalID: 6530179738444782934
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_1
      rect:
        serializedVersion: 2
        x: 70
        y: 442
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: aeace44ea01eab24e98a09e3ab838826
      internalID: 613435604629981061
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_2
      rect:
        serializedVersion: 2
        x: 140
        y: 442
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f6171b561597ff34e990fafc64e9e5be
      internalID: -3656531271222932061
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_3
      rect:
        serializedVersion: 2
        x: 210
        y: 442
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b96e63f9dd7a1634bb1068dec6fe2123
      internalID: 3930011312167392660
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_4
      rect:
        serializedVersion: 2
        x: 280
        y: 442
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2064c341aae352842bdf8aecc3646694
      internalID: -96796154962138083
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_5
      rect:
        serializedVersion: 2
        x: 350
        y: 442
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0b92a0e5bd3889343916c9dcbd0b313d
      internalID: -7054820123952135176
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_6
      rect:
        serializedVersion: 2
        x: 0
        y: 372
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8fa5709502ff56149acfe7ee5bba830d
      internalID: 4530623484691778272
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_7
      rect:
        serializedVersion: 2
        x: 70
        y: 372
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d4991a356d458a14db2449f9cf882bd1
      internalID: -5581956015464806101
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_8
      rect:
        serializedVersion: 2
        x: 140
        y: 372
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: efbed8365c486a44dbe44ff34384b3f9
      internalID: -2661919970533589962
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_9
      rect:
        serializedVersion: 2
        x: 210
        y: 372
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 68ed717e41bacf14a9de3986c709d607
      internalID: 3038985000587531000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_10
      rect:
        serializedVersion: 2
        x: 280
        y: 372
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e4ac39ef2e1efd941bb1d3f8419cfd99
      internalID: 3216594195550930655
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_11
      rect:
        serializedVersion: 2
        x: 0
        y: 302
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 872bbccb8e49e604c9c5d22efd53dd44
      internalID: -3701851516310602662
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_12
      rect:
        serializedVersion: 2
        x: 70
        y: 302
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c0cc9e3486b8a7249a100b835bd1c396
      internalID: 5893540339403691780
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_13
      rect:
        serializedVersion: 2
        x: 140
        y: 302
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c11b4d761d4975943ad9748dda2ad717
      internalID: -1254599994303000284
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_14
      rect:
        serializedVersion: 2
        x: 210
        y: 302
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2691eb86dd50ef144963a35488788cc7
      internalID: 1949730731670058941
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_15
      rect:
        serializedVersion: 2
        x: 280
        y: 302
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0625f43e77ecc24489b0a3bb77a666ba
      internalID: -3627854407871609761
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_16
      rect:
        serializedVersion: 2
        x: 0
        y: 232
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1cf9358ae9b0f0f4ead6d8fc8f09c4c4
      internalID: -1888837539432032468
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_17
      rect:
        serializedVersion: 2
        x: 70
        y: 232
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 16e67b71ffb90b14388e0b05c6d1ff11
      internalID: -1796502737815246115
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_18
      rect:
        serializedVersion: 2
        x: 140
        y: 232
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d9113cacbe0e1144e96043bb1f427d69
      internalID: 8636805145482191278
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_19
      rect:
        serializedVersion: 2
        x: 210
        y: 232
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ad6c762975b373943916ed7439c0b841
      internalID: -2218698658706304759
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_20
      rect:
        serializedVersion: 2
        x: 280
        y: 232
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ffae7dabaa180114784c2f553faf6751
      internalID: 2562169664635839239
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_21
      rect:
        serializedVersion: 2
        x: 0
        y: 162
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f8ba52ebdc3099c42aa51cd3f802fb31
      internalID: -4946619765975422720
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_22
      rect:
        serializedVersion: 2
        x: 70
        y: 162
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f8326e13ca892da4eb5447701c36010b
      internalID: 2281915828476810391
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_23
      rect:
        serializedVersion: 2
        x: 140
        y: 162
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2127a92d6ecdf714ea985afcc7c9a7f7
      internalID: -1460219255197261928
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_24
      rect:
        serializedVersion: 2
        x: 210
        y: 162
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 39ec580d1d221cd48860f0973fd1ffc1
      internalID: -272350928928935694
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_25
      rect:
        serializedVersion: 2
        x: 280
        y: 162
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: daef7de1c1ef10f42a041a6906587b3f
      internalID: 3647273195039021549
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_26
      rect:
        serializedVersion: 2
        x: 0
        y: 92
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8c4e17c25997d344cac9a74b112bfd2d
      internalID: 3504542958272820299
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_27
      rect:
        serializedVersion: 2
        x: 70
        y: 92
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 351affd9b7194914093aec3813053783
      internalID: 6202079318447832090
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_28
      rect:
        serializedVersion: 2
        x: 140
        y: 92
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9afd25d01e9dc9c4e81ebd673ee35dfd
      internalID: 6217686846738184359
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_29
      rect:
        serializedVersion: 2
        x: 210
        y: 92
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: dac19a725654a764a93ce4546e7f4f76
      internalID: -3807297515833748856
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_30
      rect:
        serializedVersion: 2
        x: 280
        y: 92
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c843a98bc0e90894a9f1691274ef367f
      internalID: 4372381551759688375
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_31
      rect:
        serializedVersion: 2
        x: 0
        y: 22
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d3f2eab2de9eaa346812069071c66f6a
      internalID: -8302254863492273045
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_32
      rect:
        serializedVersion: 2
        x: 70
        y: 22
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cd1e07f6a2b55b948b89ec68d38f7410
      internalID: -863169582964885348
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_33
      rect:
        serializedVersion: 2
        x: 140
        y: 22
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 25215da044aa2f74a8cfdeea0db62b2b
      internalID: -832198905909771304
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_34
      rect:
        serializedVersion: 2
        x: 210
        y: 22
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6f2347d505dce0947994022467242b54
      internalID: 662046916731068474
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: sheet_35
      rect:
        serializedVersion: 2
        x: 280
        y: 22
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d38645250397900418367421cb458440
      internalID: -5679681372999991991
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 0df8feae703888a4ba2e6a40a18b80ba
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      sheet_25: 3647273195039021549
      sheet_12: 5893540339403691780
      sheet_26: 3504542958272820299
      sheet_7: -5581956015464806101
      sheet_17: -1796502737815246115
      sheet_14: 1949730731670058941
      sheet_0: 6530179738444782934
      sheet_10: 3216594195550930655
      sheet_9: 3038985000587531000
      sheet_22: 2281915828476810391
      sheet_27: 6202079318447832090
      sheet_31: -8302254863492273045
      sheet_35: -5679681372999991991
      sheet_5: -7054820123952135176
      sheet_2: -3656531271222932061
      sheet_8: -2661919970533589962
      sheet_32: -863169582964885348
      sheet_24: -272350928928935694
      sheet_34: 662046916731068474
      sheet_29: -3807297515833748856
      sheet_6: 4530623484691778272
      sheet_20: 2562169664635839239
      sheet_13: -1254599994303000284
      sheet_28: 6217686846738184359
      sheet_23: -1460219255197261928
      sheet_18: 8636805145482191278
      sheet_16: -1888837539432032468
      sheet_19: -2218698658706304759
      sheet_1: 613435604629981061
      sheet_11: -3701851516310602662
      sheet_4: -96796154962138083
      sheet_3: 3930011312167392660
      sheet_30: 4372381551759688375
      sheet_21: -4946619765975422720
      sheet_15: -3627854407871609761
      sheet_33: -832198905909771304
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
