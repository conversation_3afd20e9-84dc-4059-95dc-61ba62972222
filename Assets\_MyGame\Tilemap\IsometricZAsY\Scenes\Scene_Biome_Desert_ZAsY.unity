%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0, g: 0, b: 0, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &106932605
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 106932606}
  - component: {fileID: 106932612}
  - component: {fileID: 106932611}
  - component: {fileID: 106932610}
  - component: {fileID: 106932607}
  - component: {fileID: 106932609}
  - component: {fileID: 106932608}
  m_Layer: 0
  m_Name: Tilemap - Collider - Level 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &106932606
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 106932605}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 968377523}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &106932607
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 106932605}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0351af0e3d731ca4ab7c72b47fda7daf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!66 &106932608
CompositeCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 106932605}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_GeometryType: 0
  m_GenerationType: 0
  m_EdgeRadius: 0.05
  m_ColliderPaths: []
  m_CompositePaths:
    m_Paths: []
  m_VertexDistance: 0.0005
  m_OffsetDistance: 0.000005
--- !u!50 &106932609
Rigidbody2D:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 106932605}
  m_BodyType: 2
  m_Simulated: 1
  m_UseFullKinematicContacts: 0
  m_UseAutoMass: 0
  m_Mass: 1
  m_LinearDrag: 0
  m_AngularDrag: 0.05
  m_GravityScale: 1
  m_Material: {fileID: 0}
  m_Interpolate: 0
  m_SleepingMode: 1
  m_CollisionDetection: 0
  m_Constraints: 0
--- !u!19719996 &106932610
TilemapCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 106932605}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_MaximumTileChangeCount: 1000
  m_ExtrusionFactor: 0.00001
--- !u!483693784 &106932611
TilemapRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 106932605}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 3
  m_ChunkSize: {x: 32, y: 32, z: 32}
  m_ChunkCullingBounds: {x: 0, y: 0.25, z: 0}
  m_MaxChunkCount: 16
  m_MaxFrameAge: 16
  m_SortOrder: 3
  m_Mode: 0
  m_DetectChunkCullingBounds: 0
  m_MaskInteraction: 0
--- !u!1839735485 &106932612
Tilemap:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 106932605}
  m_Enabled: 1
  m_Tiles:
  - first: {x: 3, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 1
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 1
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  m_AnimatedTiles: {}
  m_TileAssetArray:
  - m_RefCount: 9
    m_Data: {fileID: 11400000, guid: 2655b78189c934c049014795a63848c2, type: 2}
  - m_RefCount: 4
    m_Data: {fileID: 11400000, guid: 9fda3ab080ea045df984418fa0ae89fa, type: 2}
  - m_RefCount: 10
    m_Data: {fileID: 11400000, guid: 7e12fb73967d34777ba76171e8be7b04, type: 2}
  - m_RefCount: 2
    m_Data: {fileID: 11400000, guid: cbf694c6d4cf7407fa98171d5b460804, type: 2}
  m_TileSpriteArray:
  - m_RefCount: 9
    m_Data: {fileID: 21300000, guid: 8b08bcd21cb8a42fba868f390b68099b, type: 3}
  - m_RefCount: 4
    m_Data: {fileID: 21300000, guid: 6b19fa030e22a4aa7a4ebc2029911cf4, type: 3}
  - m_RefCount: 10
    m_Data: {fileID: 21300000, guid: 6b9080908d34f4ed4b111f8752045ecf, type: 3}
  - m_RefCount: 2
    m_Data: {fileID: 21300000, guid: 063edbe48872b4ab39d61247bfbc7217, type: 3}
  m_TileMatrixArray:
  - m_RefCount: 23
    m_Data:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - m_RefCount: 2
    m_Data:
      e00: -1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  m_TileColorArray:
  - m_RefCount: 25
    m_Data: {r: 1, g: 1, b: 1, a: 1}
  m_TileObjectToInstantiateArray: []
  m_AnimationFrameRate: 1
  m_Color: {r: 0.074186325, g: 1, b: 0, a: 1}
  m_Origin: {x: -2, y: -5, z: 0}
  m_Size: {x: 7, y: 9, z: 1}
  m_TileAnchor: {x: 0.5, y: 0.5, z: 0}
  m_TileOrientation: 0
  m_TileOrientationMatrix:
    e00: 1
    e01: 0
    e02: 0
    e03: 0
    e10: 0
    e11: 1
    e12: 0
    e13: 0
    e20: 0
    e21: 0
    e22: 1
    e23: 0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
--- !u!1 &141188661
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 141188662}
  - component: {fileID: 141188663}
  m_Layer: 0
  m_Name: Trigger - Ground - Base - Right
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &141188662
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 141188661}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2.5, y: -2, z: 2}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1128043138}
  - {fileID: 867642271}
  m_Father: {fileID: 0}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &141188663
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 141188661}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 21679eee47d160141b453fea1decf53a, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 1
--- !u!1001 &156691851
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 319720608}
    m_Modifications:
    - target: {fileID: 6024432824983170947, guid: f03b714ecc5c721458f27961c794d663,
        type: 3}
      propertyPath: m_SortingLayerID
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6024432824983170947, guid: f03b714ecc5c721458f27961c794d663,
        type: 3}
      propertyPath: m_SpriteSortPoint
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6024432824983170948, guid: f03b714ecc5c721458f27961c794d663,
        type: 3}
      propertyPath: m_SortingLayerID
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6024432824983170948, guid: f03b714ecc5c721458f27961c794d663,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6024432824983170949, guid: f03b714ecc5c721458f27961c794d663,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 1.27
      objectReference: {fileID: 0}
    - target: {fileID: 6024432824983170949, guid: f03b714ecc5c721458f27961c794d663,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.68
      objectReference: {fileID: 0}
    - target: {fileID: 6024432824983170949, guid: f03b714ecc5c721458f27961c794d663,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 6024432824983170949, guid: f03b714ecc5c721458f27961c794d663,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 6024432824983170949, guid: f03b714ecc5c721458f27961c794d663,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 6024432824983170949, guid: f03b714ecc5c721458f27961c794d663,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 6024432824983170949, guid: f03b714ecc5c721458f27961c794d663,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6024432824983170949, guid: f03b714ecc5c721458f27961c794d663,
        type: 3}
      propertyPath: m_RootOrder
      value: 16
      objectReference: {fileID: 0}
    - target: {fileID: 6024432824983170949, guid: f03b714ecc5c721458f27961c794d663,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6024432824983170949, guid: f03b714ecc5c721458f27961c794d663,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6024432824983170949, guid: f03b714ecc5c721458f27961c794d663,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6024432824983170950, guid: f03b714ecc5c721458f27961c794d663,
        type: 3}
      propertyPath: m_Name
      value: Rock_Purple_Large
      objectReference: {fileID: 0}
    - target: {fileID: 6024432824983170950, guid: f03b714ecc5c721458f27961c794d663,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f03b714ecc5c721458f27961c794d663, type: 3}
--- !u!4 &156691852 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 6024432824983170949, guid: f03b714ecc5c721458f27961c794d663,
    type: 3}
  m_PrefabInstance: {fileID: 156691851}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &174605339
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 174605340}
  - component: {fileID: 174605346}
  - component: {fileID: 174605345}
  - component: {fileID: 174605344}
  - component: {fileID: 174605341}
  - component: {fileID: 174605343}
  - component: {fileID: 174605342}
  m_Layer: 0
  m_Name: Tilemap - Collider - Level 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &174605340
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 174605339}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 968377523}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &174605341
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 174605339}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0351af0e3d731ca4ab7c72b47fda7daf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!66 &174605342
CompositeCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 174605339}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_GeometryType: 0
  m_GenerationType: 0
  m_EdgeRadius: 0.05
  m_ColliderPaths: []
  m_CompositePaths:
    m_Paths:
    - - {x: 3.0625, y: -1.5156144}
      - {x: 6.031247, y: -0.0312446}
      - {x: 6.0156217, y: 0}
      - {x: 6, y: 0.0000058}
      - {x: 5.9999967, y: 0.0312514}
      - {x: 5.562499, y: 0.25}
      - {x: 5.5, y: 0.2500058}
      - {x: 5.4999967, y: 0.2812514}
      - {x: 5.062499, y: 0.5}
      - {x: 4.937499, y: 0.4999994}
      - {x: 4.5, y: 0.2812466}
      - {x: 4.4999943, y: 0.25}
      - {x: 4.437499, y: 0.2499994}
      - {x: 4, y: 0.0312466}
      - {x: 3.9999945, y: 0}
      - {x: 3.9843736, y: -0.000003}
      - {x: 3.9687552, y: -0.0312524}
      - {x: 4.4218626, y: -0.2578187}
      - {x: 4, y: -0.4687534}
      - {x: 3.9999945, y: -0.5}
      - {x: 3.9374993, y: -0.5000006}
      - {x: 3.4999976, y: -0.7187488}
      - {x: 3.0624988, y: -0.5}
      - {x: 3, y: -0.49999422}
      - {x: 2.9999971, y: -0.4687486}
      - {x: 2.5624988, y: -0.25}
      - {x: 2.5, y: -0.2499942}
      - {x: 2.4999971, y: -0.2187486}
      - {x: 2.0781374, y: -0.0078063}
      - {x: 4.031247, y: 0.9687554}
      - {x: 4.0156217, y: 1}
      - {x: 4, y: 1.0000058}
      - {x: 3.999997, y: 1.0312514}
      - {x: 3.5624988, y: 1.25}
      - {x: 3.5, y: 1.2500058}
      - {x: 3.499997, y: 1.2812514}
      - {x: 3.0624988, y: 1.5}
      - {x: 2.937499, y: 1.4999994}
      - {x: 2.5, y: 1.2812467}
      - {x: 2.4999943, y: 1.25}
      - {x: 2.437499, y: 1.2499994}
      - {x: 2, y: 1.0312467}
      - {x: 1.9999943, y: 1}
      - {x: 1.937499, y: 0.9999994}
      - {x: 1.5, y: 0.7812466}
      - {x: 1.4999942, y: 0.75}
      - {x: 1.4843736, y: 0.749997}
      - {x: 1.4687552, y: 0.7187476}
      - {x: 1.9218626, y: 0.4921813}
      - {x: 1.5, y: 0.2812466}
      - {x: 1.4999942, y: 0.25}
      - {x: 1.437499, y: 0.2499994}
      - {x: 1, y: 0.0312466}
      - {x: 0.9999942, y: 0}
      - {x: 0.937499, y: -0.0000006}
      - {x: 0.5, y: -0.2187534}
      - {x: 0.5000094, y: -0.2968702}
      - {x: 2.0312474, y: 0.4687554}
      - {x: 2.0156217, y: 0.5}
      - {x: 2, y: 0.5000058}
      - {x: 1.999997, y: 0.53125143}
      - {x: 1.5781374, y: 0.7421937}
      - {x: 2.5000014, y: 1.203125}
      - {x: 2.546876, y: 1.2031256}
      - {x: 3.0000026, y: 1.4374988}
      - {x: 3.4531264, y: 1.203125}
      - {x: 3.5000012, y: 1.2031244}
      - {x: 3.9218624, y: 0.9921813}
      - {x: 3.5, y: 0.7812466}
      - {x: 3.4999945, y: 0.75}
      - {x: 3.4374993, y: 0.7499994}
      - {x: 3, y: 0.5312466}
      - {x: 2.9999943, y: 0.5}
      - {x: 2.937499, y: 0.4999994}
      - {x: 2.5, y: 0.2812466}
      - {x: 2.4999943, y: 0.25}
      - {x: 2.437499, y: 0.2499994}
      - {x: 2, y: 0.0312466}
      - {x: 1.9999943, y: 0}
      - {x: 1.9843736, y: -0.000003}
      - {x: 1.9687552, y: -0.0312524}
      - {x: 3.5000024, y: -0.7968738}
      - {x: 4.531247, y: -0.2812446}
      - {x: 4.5156217, y: -0.25}
      - {x: 4.5, y: -0.2499942}
      - {x: 4.4999967, y: -0.2187486}
      - {x: 4.078138, y: -0.0078063}
      - {x: 4.5000014, y: 0.203125}
      - {x: 4.546876, y: 0.2031256}
      - {x: 5.0000024, y: 0.4374988}
      - {x: 5.4531264, y: 0.203125}
      - {x: 5.5000014, y: 0.2031244}
      - {x: 5.9218626, y: -0.0078187}
      - {x: 5.5, y: -0.2187534}
      - {x: 5.4999943, y: -0.25}
      - {x: 5.437499, y: -0.2500006}
      - {x: 5, y: -0.4687534}
      - {x: 4.9999943, y: -0.5}
      - {x: 4.937499, y: -0.5000006}
      - {x: 4.5, y: -0.7187534}
      - {x: 4.4999943, y: -0.75}
      - {x: 4.437499, y: -0.7500006}
      - {x: 4, y: -0.9687534}
      - {x: 3.9999945, y: -1}
      - {x: 3.9374993, y: -1.0000006}
      - {x: 3.5, y: -1.2187535}
      - {x: 3.4999945, y: -1.25}
      - {x: 3.4374993, y: -1.2500006}
      - {x: 3, y: -1.4687535}
      - {x: 3.0000057, y: -1.5}
      - {x: 3.0625, y: -1.5000058}
    - - {x: -0.5312528, y: -1.7812468}
      - {x: -0.5, y: -1.7656217}
      - {x: -0.50000083, y: -1.6874985}
      - {x: -0.5624998, y: -1.59375}
      - {x: -0.9687526, y: -1}
      - {x: -1, y: -0.9999942}
      - {x: -1.000003, y: -0.9687486}
      - {x: -1.4375012, y: -0.75}
      - {x: -1.5, y: -0.7499942}
      - {x: -1.500003, y: -0.7187486}
      - {x: -1.9375012, y: -0.5}
      - {x: -2, y: -0.49999422}
      - {x: -2.000003, y: -0.4687486}
      - {x: -2.4375012, y: -0.25}
      - {x: -2.5, y: -0.2499942}
      - {x: -2.500003, y: -0.2187486}
      - {x: -2.8993526, y: -0.0190614}
      - {x: -2.4999974, y: 0.18749881}
      - {x: -2.0468736, y: -0.046875}
      - {x: -1.9999988, y: -0.0468756}
      - {x: -1.4999976, y: -0.2968739}
      - {x: -0.4999986, y: 0.203125}
      - {x: -0.45312402, y: 0.2031256}
      - {x: 0.0000026, y: 0.4374988}
      - {x: 0.3993524, y: 0.2309262}
      - {x: 0, y: 0.0312466}
      - {x: 0.0000094, y: -0.0468702}
      - {x: 0.5312474, y: 0.21875541}
      - {x: 0.5156216, y: 0.25}
      - {x: 0.5, y: 0.2500058}
      - {x: 0.49999702, y: 0.2812514}
      - {x: 0.0624988, y: 0.5}
      - {x: -0.062501, y: 0.4999994}
      - {x: -0.5, y: 0.2812466}
      - {x: -0.5000058, y: 0.25}
      - {x: -0.562501, y: 0.2499994}
      - {x: -1, y: 0.0312466}
      - {x: -1.0000058, y: 0}
      - {x: -1.062501, y: -0.0000006}
      - {x: -1.5000024, y: -0.21874881}
      - {x: -1.9375012, y: 0}
      - {x: -2, y: 0.0000058}
      - {x: -2.000003, y: 0.0312514}
      - {x: -2.4375012, y: 0.25}
      - {x: -2.562501, y: 0.2499994}
      - {x: -3, y: 0.0312466}
      - {x: -3.0000057, y: 0}
      - {x: -3.0156264, y: -0.000003}
      - {x: -3.0312448, y: -0.0312524}
      - {x: -1.0625, y: -1.0156286}
      - {x: -1.0624992, y: -1.0468764}
      - {x: -0.98437524, y: -1.1718748}
      - {x: -0.859374, y: -1.3437512}
      - {x: -0.6562502, y: -1.6406244}
    - - {x: -0.0312528, y: -1.5312468}
      - {x: 0, y: -1.5156217}
      - {x: -0.0000008, y: -1.4374985}
      - {x: -0.062499803, y: -1.34375}
      - {x: -0.4409711, y: -0.7905883}
      - {x: 0.0000026, y: -0.5625012}
      - {x: 0.4531264, y: -0.796875}
      - {x: 0.5000012, y: -0.7968756}
      - {x: 1.5000012, y: -1.296875}
      - {x: 1.5468761, y: -1.2968744}
      - {x: 2.0000026, y: -1.0625012}
      - {x: 2.44098, y: -1.2905873}
      - {x: 2.4687526, y: -1.25}
      - {x: 2.5, y: -1.2499942}
      - {x: 2.4999971, y: -1.2187486}
      - {x: 2.0624988, y: -1}
      - {x: 1.937499, y: -1.0000006}
      - {x: 1.4999976, y: -1.2187488}
      - {x: 1.0624988, y: -1}
      - {x: 1, y: -0.9999942}
      - {x: 0.999997, y: -0.9687486}
      - {x: 0.5624988, y: -0.75}
      - {x: 0.5, y: -0.7499942}
      - {x: 0.49999702, y: -0.7187486}
      - {x: 0.0624988, y: -0.5}
      - {x: -0.062501, y: -0.5000006}
      - {x: -0.5, y: -0.7187534}
      - {x: -0.5000058, y: -0.75}
      - {x: -0.5625, y: -0.7500058}
      - {x: -0.5624992, y: -0.79687643}
      - {x: -0.4843752, y: -0.9218748}
      - {x: -0.35937402, y: -1.0937512}
      - {x: -0.15625021, y: -1.3906244}
    - - {x: 2.0312462, y: -2.031248}
      - {x: 2.1562507, y: -1.890624}
      - {x: 2.3593745, y: -1.5937505}
      - {x: 2.4843745, y: -1.4218756}
      - {x: 2.5625, y: -1.2968736}
      - {x: 2.5624943, y: -1.25}
      - {x: 2.5, y: -1.2500058}
      - {x: 2.4999943, y: -1.296875}
      - {x: 2.4531238, y: -1.2968744}
      - {x: 2.440974, y: -1.2905958}
      - {x: 2.0625002, y: -1.84375}
      - {x: 2, y: -1.9375012}
      - {x: 2.000003, y: -2.0156264}
    - - {x: 2.5312462, y: -2.281248}
      - {x: 2.6562507, y: -2.140624}
      - {x: 2.8593745, y: -1.8437505}
      - {x: 2.9843745, y: -1.6718756}
      - {x: 3.0625, y: -1.5468736}
      - {x: 3.0624907, y: -1.5156298}
      - {x: 3, y: -1.5468644}
      - {x: 2.9999943, y: -1.5}
      - {x: 2.9687486, y: -1.5000024}
      - {x: 2.5625002, y: -2.09375}
      - {x: 2.5, y: -2.1875012}
      - {x: 2.500003, y: -2.2656264}
  m_VertexDistance: 0.0005
  m_OffsetDistance: 0.000005
--- !u!50 &174605343
Rigidbody2D:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 174605339}
  m_BodyType: 2
  m_Simulated: 1
  m_UseFullKinematicContacts: 0
  m_UseAutoMass: 0
  m_Mass: 1
  m_LinearDrag: 0
  m_AngularDrag: 0.05
  m_GravityScale: 1
  m_Material: {fileID: 0}
  m_Interpolate: 0
  m_SleepingMode: 1
  m_CollisionDetection: 0
  m_Constraints: 0
--- !u!19719996 &174605344
TilemapCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 174605339}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 1
  m_Offset: {x: 0, y: 0}
  m_MaximumTileChangeCount: 1000
  m_ExtrusionFactor: 0.00001
--- !u!483693784 &174605345
TilemapRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 174605339}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 2
  m_ChunkSize: {x: 32, y: 32, z: 32}
  m_ChunkCullingBounds: {x: 0, y: 0.25, z: 0}
  m_MaxChunkCount: 16
  m_MaxFrameAge: 16
  m_SortOrder: 3
  m_Mode: 0
  m_DetectChunkCullingBounds: 0
  m_MaskInteraction: 0
--- !u!1839735485 &174605346
Tilemap:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 174605339}
  m_Enabled: 1
  m_Tiles:
  - first: {x: -1, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 1
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 1
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 3
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  m_AnimatedTiles: {}
  m_TileAssetArray:
  - m_RefCount: 4
    m_Data: {fileID: 11400000, guid: cbf694c6d4cf7407fa98171d5b460804, type: 2}
  - m_RefCount: 21
    m_Data: {fileID: 11400000, guid: 7e12fb73967d34777ba76171e8be7b04, type: 2}
  - m_RefCount: 6
    m_Data: {fileID: 11400000, guid: 9fda3ab080ea045df984418fa0ae89fa, type: 2}
  - m_RefCount: 14
    m_Data: {fileID: 11400000, guid: 2655b78189c934c049014795a63848c2, type: 2}
  m_TileSpriteArray:
  - m_RefCount: 4
    m_Data: {fileID: 21300000, guid: 063edbe48872b4ab39d61247bfbc7217, type: 3}
  - m_RefCount: 21
    m_Data: {fileID: 21300000, guid: 6b9080908d34f4ed4b111f8752045ecf, type: 3}
  - m_RefCount: 6
    m_Data: {fileID: 21300000, guid: 6b19fa030e22a4aa7a4ebc2029911cf4, type: 3}
  - m_RefCount: 14
    m_Data: {fileID: 21300000, guid: 8b08bcd21cb8a42fba868f390b68099b, type: 3}
  m_TileMatrixArray:
  - m_RefCount: 43
    m_Data:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - m_RefCount: 2
    m_Data:
      e00: -1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  m_TileColorArray:
  - m_RefCount: 45
    m_Data: {r: 1, g: 1, b: 1, a: 1}
  m_TileObjectToInstantiateArray: []
  m_AnimationFrameRate: 1
  m_Color: {r: 0, g: 0.053248882, b: 1, a: 1}
  m_Origin: {x: -4, y: -7, z: 0}
  m_Size: {x: 10, y: 10, z: 1}
  m_TileAnchor: {x: 0.5, y: 0.5, z: 0}
  m_TileOrientation: 0
  m_TileOrientationMatrix:
    e00: 1
    e01: 0
    e02: 0
    e03: 0
    e10: 0
    e11: 1
    e12: 0
    e13: 0
    e20: 0
    e21: 0
    e22: 1
    e23: 0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
--- !u!1 &319720607
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 319720608}
  m_Layer: 0
  m_Name: Decorations
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &319720608
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 319720607}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1297103704}
  - {fileID: 2048416128}
  - {fileID: 1894663898}
  - {fileID: 842380177}
  - {fileID: 1190623037}
  - {fileID: 3243268130976060149}
  - {fileID: 156691852}
  - {fileID: 1533418802}
  m_Father: {fileID: 0}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &382214878
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 382214883}
  - component: {fileID: 382214882}
  - component: {fileID: 382214881}
  - component: {fileID: 382214879}
  - component: {fileID: 382214880}
  m_Layer: 0
  m_Name: Tigger - Up
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &382214879
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 382214878}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ed665cb91f11c9740a745b4cd13c92ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  height: 4
  enableCollider:
  - {fileID: 174605344}
  disableCollider:
  - {fileID: 1329646992}
--- !u!68 &382214880
EdgeCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 382214878}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 1
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_EdgeRadius: 0
  m_Points:
  - {x: -0.48714644, y: -0.0073892046}
  - {x: -0.006805162, y: 0.22699322}
--- !u!114 &382214881
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 382214878}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0351af0e3d731ca4ab7c72b47fda7daf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!212 &382214882
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 382214878}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 1
  m_Sprite: {fileID: 21300000, guid: 6b9080908d34f4ed4b111f8752045ecf, type: 3}
  m_Color: {r: 0, g: 1, b: 0.9228463, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!4 &382214883
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 382214878}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.415, y: -0.203, z: 0}
  m_LocalScale: {x: 0.9, y: 0.9, z: 1}
  m_Children: []
  m_Father: {fileID: 1818499533}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!60 &524215778 stripped
PolygonCollider2D:
  m_CorrespondingSourceObject: {fileID: 7683452085151398542, guid: 3b0788909d2477247a43cb4f4ceb765d,
    type: 3}
  m_PrefabInstance: {fileID: 842380176}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &579170498
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 217815823075059427, guid: a715e6498a8f8444e8af75aa518ea006,
        type: 3}
      propertyPath: m_Name
      value: MainCamera_IsometricPixelPerfect
      objectReference: {fileID: 0}
    - target: {fileID: 1343731013812197621, guid: a715e6498a8f8444e8af75aa518ea006,
        type: 3}
      propertyPath: followTarget
      value: 
      objectReference: {fileID: 2501001021266614076}
    - target: {fileID: 5645696305277769106, guid: a715e6498a8f8444e8af75aa518ea006,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5645696305277769106, guid: a715e6498a8f8444e8af75aa518ea006,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5645696305277769106, guid: a715e6498a8f8444e8af75aa518ea006,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -5
      objectReference: {fileID: 0}
    - target: {fileID: 5645696305277769106, guid: a715e6498a8f8444e8af75aa518ea006,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5645696305277769106, guid: a715e6498a8f8444e8af75aa518ea006,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5645696305277769106, guid: a715e6498a8f8444e8af75aa518ea006,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5645696305277769106, guid: a715e6498a8f8444e8af75aa518ea006,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5645696305277769106, guid: a715e6498a8f8444e8af75aa518ea006,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5645696305277769106, guid: a715e6498a8f8444e8af75aa518ea006,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5645696305277769106, guid: a715e6498a8f8444e8af75aa518ea006,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5645696305277769106, guid: a715e6498a8f8444e8af75aa518ea006,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: a715e6498a8f8444e8af75aa518ea006, type: 3}
--- !u!1 &628296541
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 628296546}
  - component: {fileID: 628296545}
  - component: {fileID: 628296544}
  - component: {fileID: 628296542}
  - component: {fileID: 628296543}
  m_Layer: 0
  m_Name: Tigger - Up
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &628296542
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 628296541}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ed665cb91f11c9740a745b4cd13c92ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  height: 6
  enableCollider:
  - {fileID: 106932610}
  - {fileID: 524215778}
  disableCollider:
  - {fileID: 174605344}
--- !u!68 &628296543
EdgeCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 628296541}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 1
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_EdgeRadius: 0
  m_Points:
  - {x: -0.49105328, y: -0.011752811}
  - {x: -0.011787018, y: 0.22831443}
--- !u!114 &628296544
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 628296541}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0351af0e3d731ca4ab7c72b47fda7daf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!212 &628296545
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 628296541}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 1
  m_Sprite: {fileID: 21300000, guid: 6b9080908d34f4ed4b111f8752045ecf, type: 3}
  m_Color: {r: 0, g: 1, b: 0.9228463, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!4 &628296546
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 628296541}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.444, y: -0.189, z: 0}
  m_LocalScale: {x: -0.9, y: 0.9, z: 1}
  m_Children: []
  m_Father: {fileID: 1372637016}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &842380176
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 319720608}
    m_Modifications:
    - target: {fileID: 7683452084394070184, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_SortingLayerID
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070184, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070198, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_Name
      value: Tree_Palm (3)
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070198, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -1.34
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.86
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452085151398540, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452085151398542, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_Enabled
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 3b0788909d2477247a43cb4f4ceb765d, type: 3}
--- !u!4 &842380177 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
    type: 3}
  m_PrefabInstance: {fileID: 842380176}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &867642270
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 867642271}
  - component: {fileID: 867642275}
  - component: {fileID: 867642274}
  - component: {fileID: 867642273}
  - component: {fileID: 867642272}
  m_Layer: 0
  m_Name: Tigger - Down
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &867642271
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 867642270}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.474, y: -0.229, z: 0}
  m_LocalScale: {x: -0.9, y: 0.9, z: 1}
  m_Children: []
  m_Father: {fileID: 141188662}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!68 &867642272
EdgeCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 867642270}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 1
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_EdgeRadius: 0
  m_Points:
  - {x: -0.48890123, y: -0.01199772}
  - {x: -0.00622756, y: 0.22747123}
--- !u!114 &867642273
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 867642270}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ed665cb91f11c9740a745b4cd13c92ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  height: 2
  enableCollider:
  - {fileID: 1329646992}
  disableCollider:
  - {fileID: 174605344}
--- !u!114 &867642274
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 867642270}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0351af0e3d731ca4ab7c72b47fda7daf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!212 &867642275
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 867642270}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 1
  m_Sprite: {fileID: 21300000, guid: 6b9080908d34f4ed4b111f8752045ecf, type: 3}
  m_Color: {r: 0, g: 1, b: 0.9228463, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &907868449
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 907868450}
  - component: {fileID: 907868454}
  - component: {fileID: 907868453}
  - component: {fileID: 907868452}
  - component: {fileID: 907868451}
  m_Layer: 0
  m_Name: Tigger - Down
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &907868450
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 907868449}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.494, y: -0.241, z: 0}
  m_LocalScale: {x: -0.9, y: 0.9, z: 1}
  m_Children: []
  m_Father: {fileID: 1372637016}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!68 &907868451
EdgeCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 907868449}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 1
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_EdgeRadius: 0
  m_Points:
  - {x: -0.48910055, y: -0.009800031}
  - {x: -0.013739864, y: 0.22636169}
--- !u!114 &907868452
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 907868449}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ed665cb91f11c9740a745b4cd13c92ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  height: 4
  enableCollider:
  - {fileID: 174605344}
  disableCollider:
  - {fileID: 106932610}
  - {fileID: 524215778}
--- !u!114 &907868453
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 907868449}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0351af0e3d731ca4ab7c72b47fda7daf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!212 &907868454
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 907868449}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 1
  m_Sprite: {fileID: 21300000, guid: 6b9080908d34f4ed4b111f8752045ecf, type: 3}
  m_Color: {r: 0, g: 1, b: 0.9228463, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &968377521
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 968377523}
  - component: {fileID: 968377522}
  m_Layer: 0
  m_Name: Grid - Level
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!156049354 &968377522
Grid:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 968377521}
  m_Enabled: 1
  m_CellSize: {x: 1, y: 0.5, z: 1}
  m_CellGap: {x: 0, y: 0, z: 0}
  m_CellLayout: 3
  m_CellSwizzle: 0
--- !u!4 &968377523
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 968377521}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1782783680}
  - {fileID: 1329646986}
  - {fileID: 174605340}
  - {fileID: 106932606}
  m_Father: {fileID: 0}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1127765706
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1127765711}
  - component: {fileID: 1127765710}
  - component: {fileID: 1127765709}
  - component: {fileID: 1127765708}
  - component: {fileID: 1127765707}
  m_Layer: 0
  m_Name: Tigger - Down
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!68 &1127765707
EdgeCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1127765706}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 1
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_EdgeRadius: 0
  m_Points:
  - {x: -0.48775542, y: -0.008168237}
  - {x: -0.008152625, y: 0.23060216}
--- !u!114 &1127765708
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1127765706}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ed665cb91f11c9740a745b4cd13c92ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  height: 2
  enableCollider:
  - {fileID: 1329646992}
  disableCollider:
  - {fileID: 174605344}
--- !u!114 &1127765709
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1127765706}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0351af0e3d731ca4ab7c72b47fda7daf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!212 &1127765710
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1127765706}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 1
  m_Sprite: {fileID: 21300000, guid: 6b9080908d34f4ed4b111f8752045ecf, type: 3}
  m_Color: {r: 0, g: 1, b: 0.9228463, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!4 &1127765711
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1127765706}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.447, y: -0.23, z: 0}
  m_LocalScale: {x: 0.9, y: 0.9, z: 1}
  m_Children: []
  m_Father: {fileID: 1818499533}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1128043133
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1128043138}
  - component: {fileID: 1128043137}
  - component: {fileID: 1128043136}
  - component: {fileID: 1128043134}
  - component: {fileID: 1128043135}
  m_Layer: 0
  m_Name: Tigger - Up
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1128043134
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1128043133}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ed665cb91f11c9740a745b4cd13c92ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  height: 4
  enableCollider:
  - {fileID: 174605344}
  disableCollider:
  - {fileID: 1329646992}
--- !u!68 &1128043135
EdgeCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1128043133}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 1
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_EdgeRadius: 0
  m_Points:
  - {x: -0.48069137, y: -0.007892914}
  - {x: -0.013410814, y: 0.2288851}
--- !u!114 &1128043136
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1128043133}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0351af0e3d731ca4ab7c72b47fda7daf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!212 &1128043137
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1128043133}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 1
  m_Sprite: {fileID: 21300000, guid: 6b9080908d34f4ed4b111f8752045ecf, type: 3}
  m_Color: {r: 0, g: 1, b: 0.9228463, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!4 &1128043138
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1128043133}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.456, y: -0.179, z: 0}
  m_LocalScale: {x: -0.9, y: 0.9, z: 1}
  m_Children: []
  m_Father: {fileID: 141188662}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1190623036
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 319720608}
    m_Modifications:
    - target: {fileID: 959392640622242604, guid: 6ed2348d3a21bb14199acf0512d8afb4,
        type: 3}
      propertyPath: m_Name
      value: Tent
      objectReference: {fileID: 0}
    - target: {fileID: 959392640622242604, guid: 6ed2348d3a21bb14199acf0512d8afb4,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2161079991501458906, guid: 6ed2348d3a21bb14199acf0512d8afb4,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.239
      objectReference: {fileID: 0}
    - target: {fileID: 2161079991501458906, guid: 6ed2348d3a21bb14199acf0512d8afb4,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -3.238
      objectReference: {fileID: 0}
    - target: {fileID: 2161079991501458906, guid: 6ed2348d3a21bb14199acf0512d8afb4,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 2161079991501458906, guid: 6ed2348d3a21bb14199acf0512d8afb4,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2161079991501458906, guid: 6ed2348d3a21bb14199acf0512d8afb4,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2161079991501458906, guid: 6ed2348d3a21bb14199acf0512d8afb4,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2161079991501458906, guid: 6ed2348d3a21bb14199acf0512d8afb4,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2161079991501458906, guid: 6ed2348d3a21bb14199acf0512d8afb4,
        type: 3}
      propertyPath: m_RootOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 2161079991501458906, guid: 6ed2348d3a21bb14199acf0512d8afb4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2161079991501458906, guid: 6ed2348d3a21bb14199acf0512d8afb4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2161079991501458906, guid: 6ed2348d3a21bb14199acf0512d8afb4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7416450066624897328, guid: 6ed2348d3a21bb14199acf0512d8afb4,
        type: 3}
      propertyPath: m_SortingLayerID
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7416450066624897328, guid: 6ed2348d3a21bb14199acf0512d8afb4,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 6ed2348d3a21bb14199acf0512d8afb4, type: 3}
--- !u!4 &1190623037 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 2161079991501458906, guid: 6ed2348d3a21bb14199acf0512d8afb4,
    type: 3}
  m_PrefabInstance: {fileID: 1190623036}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1297103703
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 319720608}
    m_Modifications:
    - target: {fileID: 7683452084394070184, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_SortingLayerID
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070184, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070198, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_Name
      value: Tree_Palm
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.517
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -2.598
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 3b0788909d2477247a43cb4f4ceb765d, type: 3}
--- !u!4 &1297103704 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
    type: 3}
  m_PrefabInstance: {fileID: 1297103703}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1329646985
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1329646986}
  - component: {fileID: 1329646989}
  - component: {fileID: 1329646988}
  - component: {fileID: 1329646987}
  - component: {fileID: 1329646992}
  - component: {fileID: 1329646991}
  - component: {fileID: 1329646990}
  m_Layer: 0
  m_Name: Tilemap - Collider - Base
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1329646986
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1329646985}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 968377523}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1329646987
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1329646985}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0351af0e3d731ca4ab7c72b47fda7daf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!483693784 &1329646988
TilemapRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1329646985}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 1
  m_ChunkSize: {x: 32, y: 32, z: 32}
  m_ChunkCullingBounds: {x: 0, y: 0.25, z: 0}
  m_MaxChunkCount: 16
  m_MaxFrameAge: 16
  m_SortOrder: 3
  m_Mode: 0
  m_DetectChunkCullingBounds: 0
  m_MaskInteraction: 0
--- !u!1839735485 &1329646989
Tilemap:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1329646985}
  m_Enabled: 1
  m_Tiles:
  - first: {x: -6, y: -11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: -11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: -11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: -11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -11, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -7, y: -10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -7, y: -9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -7, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -7, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -7, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -7, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -7, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -7, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -7, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -7, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -7, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -7, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -7, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -7, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -7, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 0
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -6, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 1
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 2
      m_TileSpriteIndex: 2
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  m_AnimatedTiles: {}
  m_TileAssetArray:
  - m_RefCount: 43
    m_Data: {fileID: 11400000, guid: 2655b78189c934c049014795a63848c2, type: 2}
  - m_RefCount: 43
    m_Data: {fileID: 11400000, guid: 7e12fb73967d34777ba76171e8be7b04, type: 2}
  - m_RefCount: 6
    m_Data: {fileID: 11400000, guid: 9fda3ab080ea045df984418fa0ae89fa, type: 2}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  m_TileSpriteArray:
  - m_RefCount: 43
    m_Data: {fileID: 21300000, guid: 8b08bcd21cb8a42fba868f390b68099b, type: 3}
  - m_RefCount: 43
    m_Data: {fileID: 21300000, guid: 6b9080908d34f4ed4b111f8752045ecf, type: 3}
  - m_RefCount: 6
    m_Data: {fileID: 21300000, guid: 6b19fa030e22a4aa7a4ebc2029911cf4, type: 3}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  m_TileMatrixArray:
  - m_RefCount: 92
    m_Data:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  m_TileColorArray:
  - m_RefCount: 92
    m_Data: {r: 1, g: 1, b: 1, a: 1}
  m_TileObjectToInstantiateArray: []
  m_AnimationFrameRate: 1
  m_Color: {r: 1, g: 0, b: 0, a: 1}
  m_Origin: {x: -11, y: -13, z: -1}
  m_Size: {x: 21, y: 18, z: 2}
  m_TileAnchor: {x: 0.5, y: 0.5, z: 0}
  m_TileOrientation: 0
  m_TileOrientationMatrix:
    e00: 1
    e01: 0
    e02: 0
    e03: 0
    e10: 0
    e11: 1
    e12: 0
    e13: 0
    e20: 0
    e21: 0
    e22: 1
    e23: 0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
--- !u!66 &1329646990
CompositeCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1329646985}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_GeometryType: 0
  m_GenerationType: 0
  m_EdgeRadius: 0.05
  m_ColliderPaths:
  - m_Collider: {fileID: 1329646992}
    m_ColliderPaths:
    - - X: 19999904
        Y: -40468704
      - X: 90312392
        Y: -5312284
      - X: 90156112
        Y: -5000000
      - X: 90000000
        Y: -4999767
      - X: 89999880
        Y: -4687444
      - X: 85624952
        Y: -2500000
      - X: 85000000
        Y: -2499768
      - X: 84999880
        Y: -2187444
      - X: 80624952
        Y: 0
      - X: 80000000
        Y: 232
      - X: 79999880
        Y: 312556
      - X: 75624952
        Y: 2500000
      - X: 75000000
        Y: 2500232
      - X: 74999880
        Y: 2812556
      - X: 70624952
        Y: 5000000
      - X: 70000000
        Y: 5000232
      - X: 69999880
        Y: 5312556
      - X: 65624952
        Y: 7500000
      - X: 65000000
        Y: 7500232
      - X: 64999880
        Y: 7812556
      - X: 60624952
        Y: 10000000
      - X: 60000000
        Y: 10000232
      - X: 59999880
        Y: 10312556
      - X: 55624952
        Y: 12500000
      - X: 55000000
        Y: 12500232
      - X: 54999880
        Y: 12812556
      - X: 50624952
        Y: 15000000
      - X: 50000000
        Y: 15000232
      - X: 49999880
        Y: 15312556
      - X: 45624952
        Y: 17500000
      - X: 45000000
        Y: 17500232
      - X: 44999880
        Y: 17812556
      - X: 40624952
        Y: 20000000
      - X: 40000000
        Y: 20000232
      - X: 39999880
        Y: 20312556
      - X: 35624952
        Y: 22500000
      - X: 35000000
        Y: 22500232
      - X: 34999880
        Y: 22812556
      - X: 30624952
        Y: 25000000
      - X: 30000000
        Y: 25000232
      - X: 29999880
        Y: 25312556
      - X: 25624952
        Y: 27500000
      - X: 25000000
        Y: 27500232
      - X: 24999880
        Y: 27812556
      - X: 20624952
        Y: 30000000
      - X: 20000000
        Y: 30000232
      - X: 19999880
        Y: 30312556
      - X: 15624952
        Y: 32500000
      - X: 14374960
        Y: 32499976
      - X: 10000000
        Y: 30312364
      - X: 9999768
        Y: 30000000
      - X: 9374960
        Y: 29999976
      - X: 5000000
        Y: 27812364
      - X: 4999768
        Y: 27500000
      - X: 4374960
        Y: 27499976
      - X: 0
        Y: 25312364
      - X: -232
        Y: 25000000
      - X: -625040
        Y: 24999976
      - X: -5000000
        Y: 22812364
      - X: -5000232
        Y: 22500000
      - X: -5625040
        Y: 22499976
      - X: -10000000
        Y: 20312364
      - X: -10000232
        Y: 20000000
      - X: -10625040
        Y: 19999976
      - X: -15000000
        Y: 17812364
      - X: -15000232
        Y: 17500000
      - X: -15625040
        Y: 17499976
      - X: -20000000
        Y: 15312364
      - X: -20000232
        Y: 15000000
      - X: -20625040
        Y: 14999976
      - X: -25000000
        Y: 12812364
      - X: -25000232
        Y: 12500000
      - X: -25625040
        Y: 12499976
      - X: -30000000
        Y: 10312364
      - X: -30000232
        Y: 10000000
      - X: -30625040
        Y: 9999976
      - X: -35000000
        Y: 7812364
      - X: -35000232
        Y: 7500000
      - X: -35625040
        Y: 7499976
      - X: -40000000
        Y: 5312364
      - X: -40000232
        Y: 5000000
      - X: -40625040
        Y: 4999976
      - X: -45000000
        Y: 2812364
      - X: -45000232
        Y: 2500000
      - X: -45625040
        Y: 2499976
      - X: -50000000
        Y: 312364
      - X: -50000232
        Y: 0
      - X: -50625040
        Y: -24
      - X: -55000000
        Y: -2187636
      - X: -55000232
        Y: -2500000
      - X: -55156304
        Y: -2500120
      - X: -55312292
        Y: -2812596
    - - X: 20000096
        Y: -39687452
      - X: 15624952
        Y: -37500000
      - X: 15000000
        Y: -37499768
      - X: 14999880
        Y: -37187444
      - X: 10624952
        Y: -35000000
      - X: 10000000
        Y: -34999768
      - X: 9999880
        Y: -34687444
      - X: 5624952
        Y: -32500000
      - X: 5000000
        Y: -32499768
      - X: 4999880
        Y: -32187444
      - X: 624952
        Y: -30000000
      - X: 0
        Y: -29999768
      - X: -120
        Y: -29687444
      - X: -4375048
        Y: -27500000
      - X: -5000000
        Y: -27499768
      - X: -5000120
        Y: -27187444
      - X: -9375048
        Y: -25000000
      - X: -10000000
        Y: -24999768
      - X: -10000120
        Y: -24687444
      - X: -14375048
        Y: -22500000
      - X: -15000000
        Y: -22499768
      - X: -15000120
        Y: -22187444
      - X: -19375048
        Y: -20000000
      - X: -20000000
        Y: -19999768
      - X: -20000120
        Y: -19687444
      - X: -24375048
        Y: -17500000
      - X: -25000000
        Y: -17499768
      - X: -25000120
        Y: -17187444
      - X: -29375048
        Y: -15000000
      - X: -30000000
        Y: -14999768
      - X: -30000120
        Y: -14687444
      - X: -34375048
        Y: -12500000
      - X: -35000000
        Y: -12499768
      - X: -35000120
        Y: -12187444
      - X: -39375048
        Y: -10000000
      - X: -40000000
        Y: -9999768
      - X: -40000120
        Y: -9687444
      - X: -44375048
        Y: -7500000
      - X: -45000000
        Y: -7499768
      - X: -45000120
        Y: -7187444
      - X: -49375048
        Y: -5000000
      - X: -50000000
        Y: -4999768
      - X: -50000120
        Y: -4687444
      - X: -54218254
        Y: -2577877
      - X: 10000056
        Y: 29531250
      - X: 10468790
        Y: 29531274
      - X: 15000104
        Y: 31874952
      - X: 19531306
        Y: 29531250
      - X: 20000041
        Y: 29531226
      - X: 89218254
        Y: -5078373
      - X: 85000000
        Y: -7187636
      - X: 84999768
        Y: -7500000
      - X: 84374960
        Y: -7500024
      - X: 80000000
        Y: -9687636
      - X: 79999768
        Y: -10000000
      - X: 79374960
        Y: -10000024
      - X: 75000000
        Y: -12187636
      - X: 74999768
        Y: -12500000
      - X: 74374960
        Y: -12500024
      - X: 70000000
        Y: -14687636
      - X: 69999768
        Y: -15000000
      - X: 69374960
        Y: -15000024
      - X: 65000000
        Y: -17187636
      - X: 64999768
        Y: -17500000
      - X: 64374960
        Y: -17500024
      - X: 60000000
        Y: -19687636
      - X: 59999768
        Y: -20000000
      - X: 59374960
        Y: -20000024
      - X: 55000000
        Y: -22187636
      - X: 54999768
        Y: -22500000
      - X: 54374960
        Y: -22500024
      - X: 50000000
        Y: -24687636
      - X: 49999768
        Y: -25000000
      - X: 49374960
        Y: -25000024
      - X: 45000000
        Y: -27187636
      - X: 44999768
        Y: -27500000
      - X: 44374960
        Y: -27500024
      - X: 40000000
        Y: -29687636
      - X: 39999768
        Y: -30000000
      - X: 39374960
        Y: -30000024
      - X: 35000000
        Y: -32187636
      - X: 34999768
        Y: -32500000
      - X: 34374960
        Y: -32500024
      - X: 30000000
        Y: -34687636
      - X: 29999768
        Y: -35000000
      - X: 29374960
        Y: -35000024
      - X: 25000000
        Y: -37187636
      - X: 24999768
        Y: -37500000
      - X: 24374960
        Y: -37500024
    - - X: 25000000
        Y: -22968326
      - X: 60312396
        Y: -5312284
      - X: 60156112
        Y: -5000000
      - X: 60000000
        Y: -4999767
      - X: 59999880
        Y: -4687444
      - X: 55624952
        Y: -2500000
      - X: 55000000
        Y: -2499768
      - X: 54999880
        Y: -2187444
      - X: 50624952
        Y: 0
      - X: 49374960
        Y: -24
      - X: 44999904
        Y: -2187452
      - X: 40624952
        Y: 0
      - X: 40000000
        Y: 232
      - X: 39999880
        Y: 312556
      - X: 35781746
        Y: 2422123
      - X: 40312396
        Y: 4687716
      - X: 40156112
        Y: 5000000
      - X: 40000000
        Y: 5000233
      - X: 39999880
        Y: 5312556
      - X: 35624952
        Y: 7500000
      - X: 35000000
        Y: 7500232
      - X: 34999880
        Y: 7812556
      - X: 30624952
        Y: 10000000
      - X: 29374960
        Y: 9999976
      - X: 25000000
        Y: 7812364
      - X: 24999768
        Y: 7500000
      - X: 24374960
        Y: 7499976
      - X: 20000000
        Y: 5312364
      - X: 19999768
        Y: 5000000
      - X: 19374960
        Y: 4999976
      - X: 14999904
        Y: 2812548
      - X: 10624952
        Y: 5000000
      - X: 10000000
        Y: 5000232
      - X: 9999880
        Y: 5312556
      - X: 5624952
        Y: 7500000
      - X: 5000000
        Y: 7500232
      - X: 4999880
        Y: 7812556
      - X: 624952
        Y: 10000000
      - X: -625040
        Y: 9999976
      - X: -5000000
        Y: 7812364
      - X: -5000232
        Y: 7500000
      - X: -5625040
        Y: 7499976
      - X: -10000000
        Y: 5312364
      - X: -10000232
        Y: 5000000
      - X: -10625040
        Y: 4999976
      - X: -15000000
        Y: 2812364
      - X: -15000232
        Y: 2500000
      - X: -15625040
        Y: 2499976
      - X: -20000000
        Y: 312364
      - X: -20000232
        Y: 0
      - X: -20625040
        Y: -24
      - X: -25000000
        Y: -2187636
      - X: -25000232
        Y: -2500000
      - X: -25625040
        Y: -2500024
      - X: -30000000
        Y: -4687636
      - X: -30000232
        Y: -5000000
      - X: -30156306
        Y: -5000120
      - X: -30312292
        Y: -5312596
      - X: -5000000
        Y: -17968326
      - X: -5000120
        Y: -17187444
      - X: -9375048
        Y: -15000000
      - X: -10000000
        Y: -14999768
      - X: -10000120
        Y: -14687444
      - X: -14375048
        Y: -12500000
      - X: -15000000
        Y: -12499768
      - X: -15000120
        Y: -12187444
      - X: -19375048
        Y: -10000000
      - X: -20000000
        Y: -9999768
      - X: -20000120
        Y: -9687444
      - X: -24375048
        Y: -7500000
      - X: -25000000
        Y: -7499768
      - X: -25000120
        Y: -7187444
      - X: -29218254
        Y: -5077877
      - X: -4999944
        Y: 7031250
      - X: -4531210
        Y: 7031274
      - X: 104
        Y: 9374952
      - X: 4531306
        Y: 7031250
      - X: 5000047
        Y: 7031226
      - X: 15000096
        Y: 2031291
      - X: 25000056
        Y: 7031250
      - X: 25468790
        Y: 7031274
      - X: 30000104
        Y: 9374952
      - X: 34531304
        Y: 7031250
      - X: 35000041
        Y: 7031226
      - X: 39218254
        Y: 4921627
      - X: 35000000
        Y: 2812364
      - X: 34999768
        Y: 2500000
      - X: 34843696
        Y: 2499880
      - X: 34687708
        Y: 2187404
      - X: 45000048
        Y: -2968750
      - X: 45468792
        Y: -2968726
      - X: 50000104
        Y: -625048
      - X: 54531304
        Y: -2968750
      - X: 55000041
        Y: -2968774
      - X: 59218254
        Y: -5078373
      - X: 55000000
        Y: -7187636
      - X: 54999768
        Y: -7500000
      - X: 54374960
        Y: -7500024
      - X: 50000000
        Y: -9687636
      - X: 49999768
        Y: -10000000
      - X: 49374960
        Y: -10000024
      - X: 45000000
        Y: -12187636
      - X: 44999768
        Y: -12500000
      - X: 44374960
        Y: -12500024
      - X: 40000000
        Y: -14687636
      - X: 39999768
        Y: -15000000
      - X: 39374960
        Y: -15000024
      - X: 35000000
        Y: -17187636
      - X: 34999768
        Y: -17500000
      - X: 34374960
        Y: -17500024
      - X: 30000000
        Y: -19687636
      - X: 29999768
        Y: -20000000
      - X: 29374960
        Y: -20000024
      - X: 25000000
        Y: -22187636
    - - X: 20000000
        Y: -20468326
      - X: 25312396
        Y: -17812284
      - X: 25156114
        Y: -17500000
      - X: 25000000
        Y: -17499767
      - X: 24999880
        Y: -17187444
      - X: 20624952
        Y: -15000000
      - X: 19374960
        Y: -15000024
      - X: 14999904
        Y: -17187452
      - X: 10624952
        Y: -15000000
      - X: 10000000
        Y: -14999768
      - X: 9999880
        Y: -14687444
      - X: 5624952
        Y: -12500000
      - X: 5000000
        Y: -12499768
      - X: 4999880
        Y: -12187444
      - X: 624952
        Y: -10000000
      - X: -625040
        Y: -10000024
      - X: -5000000
        Y: -12187636
      - X: -5000232
        Y: -12500000
      - X: -5156306
        Y: -12500120
      - X: -5312292
        Y: -12812596
      - X: 0
        Y: -15468326
      - X: -120
        Y: -14687444
      - X: -3993164
        Y: -12690422
      - X: 104
        Y: -10625048
      - X: 4531306
        Y: -12968750
      - X: 5000048
        Y: -12968774
      - X: 15000048
        Y: -17968750
      - X: 15468790
        Y: -17968726
      - X: 20000104
        Y: -15625048
      - X: 23993164
        Y: -17690918
      - X: 20000000
        Y: -19687636
  m_CompositePaths:
    m_Paths:
    - - {x: 1.9999893, y: -4.0468698}
      - {x: 9.031238, y: -0.5312257}
      - {x: 9.01561, y: -0.5}
      - {x: 9, y: -0.4999737}
      - {x: 8.999987, y: -0.4687437}
      - {x: 8.562494, y: -0.25}
      - {x: 8.5, y: -0.24997391}
      - {x: 8.499987, y: -0.2187437}
      - {x: 8.062494, y: 0}
      - {x: 8, y: 0.0000261}
      - {x: 7.9999866, y: 0.0312563}
      - {x: 7.5624943, y: 0.25}
      - {x: 7.5, y: 0.2500261}
      - {x: 7.4999866, y: 0.28125632}
      - {x: 7.0624943, y: 0.5}
      - {x: 7, y: 0.5000261}
      - {x: 6.9999866, y: 0.5312563}
      - {x: 6.5624943, y: 0.75}
      - {x: 6.5, y: 0.7500261}
      - {x: 6.4999866, y: 0.7812563}
      - {x: 6.0624943, y: 1}
      - {x: 6, y: 1.0000261}
      - {x: 5.9999866, y: 1.0312563}
      - {x: 5.5624943, y: 1.25}
      - {x: 5.5, y: 1.2500261}
      - {x: 5.4999866, y: 1.2812563}
      - {x: 5.0624943, y: 1.5}
      - {x: 5, y: 1.5000261}
      - {x: 4.9999866, y: 1.5312563}
      - {x: 4.5624943, y: 1.75}
      - {x: 4.5, y: 1.750026}
      - {x: 4.4999866, y: 1.7812564}
      - {x: 4.0624943, y: 2}
      - {x: 4, y: 2.000026}
      - {x: 3.9999864, y: 2.0312564}
      - {x: 3.5624945, y: 2.25}
      - {x: 3.5, y: 2.250026}
      - {x: 3.4999864, y: 2.2812564}
      - {x: 3.0624948, y: 2.5}
      - {x: 3, y: 2.500026}
      - {x: 2.9999864, y: 2.5312564}
      - {x: 2.5624945, y: 2.75}
      - {x: 2.5, y: 2.750026}
      - {x: 2.4999864, y: 2.7812564}
      - {x: 2.0624945, y: 3}
      - {x: 2, y: 3.000026}
      - {x: 1.9999864, y: 3.0312564}
      - {x: 1.5624946, y: 3.25}
      - {x: 1.4374955, y: 3.2499971}
      - {x: 1, y: 3.0312347}
      - {x: 0.9999739, y: 3}
      - {x: 0.9374955, y: 2.9999971}
      - {x: 0.5, y: 2.7812347}
      - {x: 0.4999739, y: 2.75}
      - {x: 0.4374955, y: 2.7499971}
      - {x: 0, y: 2.5312347}
      - {x: -0.0000261, y: 2.5}
      - {x: -0.0625045, y: 2.4999971}
      - {x: -0.5, y: 2.2812347}
      - {x: -0.5000261, y: 2.25}
      - {x: -0.56250453, y: 2.2499971}
      - {x: -1, y: 2.0312347}
      - {x: -1.0000261, y: 2}
      - {x: -1.0625045, y: 1.9999973}
      - {x: -1.5, y: 1.7812349}
      - {x: -1.5000261, y: 1.75}
      - {x: -1.5625045, y: 1.7499973}
      - {x: -2, y: 1.5312347}
      - {x: -2.000026, y: 1.5}
      - {x: -2.0625045, y: 1.4999973}
      - {x: -2.5, y: 1.2812347}
      - {x: -2.500026, y: 1.25}
      - {x: -2.5625045, y: 1.2499973}
      - {x: -3, y: 1.0312347}
      - {x: -3.000026, y: 1}
      - {x: -3.0625045, y: 0.9999973}
      - {x: -3.5, y: 0.7812347}
      - {x: -3.500026, y: 0.75}
      - {x: -3.5625045, y: 0.7499973}
      - {x: -4, y: 0.5312347}
      - {x: -4.000026, y: 0.5}
      - {x: -4.0625043, y: 0.49999732}
      - {x: -4.5, y: 0.2812347}
      - {x: -4.500026, y: 0.25}
      - {x: -4.5625043, y: 0.2499973}
      - {x: -5, y: 0.0312347}
      - {x: -5.000026, y: 0}
      - {x: -5.0625043, y: -0.0000027}
      - {x: -5.5, y: -0.2187653}
      - {x: -5.500026, y: -0.25}
      - {x: -5.515631, y: -0.2500135}
      - {x: -5.5312266, y: -0.2812608}
    - - {x: 2.0000107, y: -3.9687448}
      - {x: 1.5624946, y: -3.75}
      - {x: 1.5, y: -3.749974}
      - {x: 1.4999865, y: -3.7187436}
      - {x: 1.0624946, y: -3.5}
      - {x: 1, y: -3.499974}
      - {x: 0.9999865, y: -3.4687436}
      - {x: 0.56249464, y: -3.25}
      - {x: 0.5, y: -3.249974}
      - {x: 0.4999865, y: -3.2187436}
      - {x: 0.062494602, y: -3}
      - {x: 0, y: -2.999974}
      - {x: -0.0000135, y: -2.9687436}
      - {x: -0.4375054, y: -2.75}
      - {x: -0.5, y: -2.749974}
      - {x: -0.50001353, y: -2.7187436}
      - {x: -0.9375054, y: -2.5}
      - {x: -1, y: -2.499974}
      - {x: -1.0000135, y: -2.4687436}
      - {x: -1.4375054, y: -2.25}
      - {x: -1.5, y: -2.249974}
      - {x: -1.5000135, y: -2.2187436}
      - {x: -1.9375054, y: -2}
      - {x: -2, y: -1.999974}
      - {x: -2.0000136, y: -1.9687437}
      - {x: -2.4375055, y: -1.75}
      - {x: -2.5, y: -1.749974}
      - {x: -2.5000136, y: -1.7187436}
      - {x: -2.9375055, y: -1.5}
      - {x: -3, y: -1.4999739}
      - {x: -3.0000136, y: -1.4687437}
      - {x: -3.4375057, y: -1.25}
      - {x: -3.5, y: -1.2499739}
      - {x: -3.5000136, y: -1.2187437}
      - {x: -3.9375057, y: -1}
      - {x: -4, y: -0.9999739}
      - {x: -4.000014, y: -0.9687437}
      - {x: -4.4375057, y: -0.75}
      - {x: -4.5, y: -0.7499739}
      - {x: -4.500014, y: -0.7187437}
      - {x: -4.9375057, y: -0.5}
      - {x: -5, y: -0.4999739}
      - {x: -5.000014, y: -0.4687437}
      - {x: -5.421819, y: -0.2577846}
      - {x: 1.0000063, y: 2.953125}
      - {x: 1.0468795, y: 2.9531276}
      - {x: 1.5000117, y: 3.1874948}
      - {x: 1.9531312, y: 2.953125}
      - {x: 2.0000045, y: 2.9531224}
      - {x: 8.92182, y: -0.5078404}
      - {x: 8.5, y: -0.7187653}
      - {x: 8.499973, y: -0.75}
      - {x: 8.437495, y: -0.7500027}
      - {x: 8, y: -0.9687653}
      - {x: 7.999974, y: -1}
      - {x: 7.937495, y: -1.0000027}
      - {x: 7.5, y: -1.2187653}
      - {x: 7.499974, y: -1.25}
      - {x: 7.437495, y: -1.2500027}
      - {x: 7, y: -1.4687653}
      - {x: 6.999974, y: -1.5}
      - {x: 6.937495, y: -1.5000027}
      - {x: 6.5, y: -1.7187653}
      - {x: 6.4999743, y: -1.75}
      - {x: 6.4374957, y: -1.7500029}
      - {x: 6, y: -1.9687653}
      - {x: 5.9999743, y: -2}
      - {x: 5.9374957, y: -2.0000029}
      - {x: 5.5, y: -2.2187653}
      - {x: 5.4999743, y: -2.25}
      - {x: 5.4374957, y: -2.2500029}
      - {x: 5, y: -2.4687653}
      - {x: 4.9999743, y: -2.5}
      - {x: 4.9374957, y: -2.5000029}
      - {x: 4.5, y: -2.7187653}
      - {x: 4.4999743, y: -2.75}
      - {x: 4.4374957, y: -2.7500029}
      - {x: 4, y: -2.9687653}
      - {x: 3.999974, y: -3}
      - {x: 3.9374957, y: -3.0000029}
      - {x: 3.5, y: -3.2187653}
      - {x: 3.499974, y: -3.25}
      - {x: 3.4374957, y: -3.2500029}
      - {x: 3, y: -3.4687653}
      - {x: 2.999974, y: -3.5}
      - {x: 2.9374957, y: -3.5000029}
      - {x: 2.5, y: -3.7187653}
      - {x: 2.499974, y: -3.75}
      - {x: 2.4374957, y: -3.7500029}
    - - {x: 2.5, y: -2.2968273}
      - {x: 6.0312386, y: -0.5312257}
      - {x: 6.0156097, y: -0.5}
      - {x: 6, y: -0.4999737}
      - {x: 5.9999866, y: -0.4687437}
      - {x: 5.5624943, y: -0.25}
      - {x: 5.5, y: -0.24997391}
      - {x: 5.4999866, y: -0.2187437}
      - {x: 5.0624943, y: 0}
      - {x: 4.9374957, y: -0.0000027}
      - {x: 4.499989, y: -0.2187446}
      - {x: 4.0624943, y: 0}
      - {x: 4, y: 0.0000261}
      - {x: 3.9999864, y: 0.0312563}
      - {x: 3.5781808, y: 0.24221541}
      - {x: 4.0312386, y: 0.46877432}
      - {x: 4.0156097, y: 0.5}
      - {x: 4, y: 0.5000263}
      - {x: 3.9999864, y: 0.5312563}
      - {x: 3.5624945, y: 0.75}
      - {x: 3.5, y: 0.7500261}
      - {x: 3.4999864, y: 0.7812563}
      - {x: 3.0624948, y: 1}
      - {x: 2.9374957, y: 0.9999973}
      - {x: 2.5, y: 0.7812347}
      - {x: 2.499974, y: 0.75}
      - {x: 2.4374957, y: 0.7499973}
      - {x: 2, y: 0.5312347}
      - {x: 1.999974, y: 0.5}
      - {x: 1.9374956, y: 0.49999732}
      - {x: 1.4999893, y: 0.2812554}
      - {x: 1.0624946, y: 0.5}
      - {x: 1, y: 0.5000261}
      - {x: 0.9999865, y: 0.5312563}
      - {x: 0.56249464, y: 0.75}
      - {x: 0.5, y: 0.7500261}
      - {x: 0.4999865, y: 0.7812563}
      - {x: 0.062494602, y: 1}
      - {x: -0.0625045, y: 0.9999973}
      - {x: -0.5, y: 0.7812347}
      - {x: -0.5000261, y: 0.75}
      - {x: -0.56250453, y: 0.7499973}
      - {x: -1, y: 0.5312347}
      - {x: -1.0000261, y: 0.5}
      - {x: -1.0625045, y: 0.49999732}
      - {x: -1.5, y: 0.2812347}
      - {x: -1.5000261, y: 0.25}
      - {x: -1.5625045, y: 0.2499973}
      - {x: -2, y: 0.0312347}
      - {x: -2.000026, y: 0}
      - {x: -2.0625045, y: -0.0000027}
      - {x: -2.5, y: -0.2187653}
      - {x: -2.500026, y: -0.25}
      - {x: -2.5625045, y: -0.2500027}
      - {x: -3, y: -0.46876532}
      - {x: -3.000026, y: -0.5}
      - {x: -3.0156312, y: -0.50001353}
      - {x: -3.0312266, y: -0.5312608}
      - {x: -0.5, y: -1.7968272}
      - {x: -0.50001353, y: -1.7187436}
      - {x: -0.9375054, y: -1.5}
      - {x: -1, y: -1.4999739}
      - {x: -1.0000135, y: -1.4687437}
      - {x: -1.4375054, y: -1.25}
      - {x: -1.5, y: -1.2499739}
      - {x: -1.5000135, y: -1.2187437}
      - {x: -1.9375054, y: -1}
      - {x: -2, y: -0.9999739}
      - {x: -2.0000136, y: -0.9687437}
      - {x: -2.4375055, y: -0.75}
      - {x: -2.5, y: -0.7499739}
      - {x: -2.5000136, y: -0.7187437}
      - {x: -2.9218192, y: -0.5077846}
      - {x: -0.4999937, y: 0.703125}
      - {x: -0.4531205, y: 0.7031277}
      - {x: 0.0000117, y: 0.93749464}
      - {x: 0.45313132, y: 0.703125}
      - {x: 0.5000053, y: 0.7031223}
      - {x: 1.5000108, y: 0.2031296}
      - {x: 2.5000064, y: 0.703125}
      - {x: 2.5468795, y: 0.7031277}
      - {x: 3.0000117, y: 0.93749464}
      - {x: 3.4531312, y: 0.703125}
      - {x: 3.5000048, y: 0.7031223}
      - {x: 3.9218192, y: 0.4921596}
      - {x: 3.5, y: 0.2812347}
      - {x: 3.499974, y: 0.25}
      - {x: 3.4843688, y: 0.2499865}
      - {x: 3.4687736, y: 0.2187392}
      - {x: 4.5000057, y: -0.296875}
      - {x: 4.54688, y: -0.29687232}
      - {x: 5.0000114, y: -0.0625054}
      - {x: 5.453131, y: -0.296875}
      - {x: 5.500005, y: -0.2968777}
      - {x: 5.921819, y: -0.5078404}
      - {x: 5.5, y: -0.7187653}
      - {x: 5.4999743, y: -0.75}
      - {x: 5.4374957, y: -0.7500027}
      - {x: 5, y: -0.9687653}
      - {x: 4.9999743, y: -1}
      - {x: 4.9374957, y: -1.0000027}
      - {x: 4.5, y: -1.2187653}
      - {x: 4.4999743, y: -1.25}
      - {x: 4.4374957, y: -1.2500027}
      - {x: 4, y: -1.4687653}
      - {x: 3.999974, y: -1.5}
      - {x: 3.9374957, y: -1.5000027}
      - {x: 3.5, y: -1.7187653}
      - {x: 3.499974, y: -1.75}
      - {x: 3.4374957, y: -1.7500029}
      - {x: 3, y: -1.9687653}
      - {x: 2.999974, y: -2}
      - {x: 2.9374957, y: -2.0000029}
      - {x: 2.5, y: -2.2187653}
    - - {x: 2, y: -2.0468273}
      - {x: 2.5312383, y: -1.7812256}
      - {x: 2.5156097, y: -1.75}
      - {x: 2.5, y: -1.7499737}
      - {x: 2.4999864, y: -1.7187436}
      - {x: 2.0624945, y: -1.5}
      - {x: 1.9374956, y: -1.5000027}
      - {x: 1.4999893, y: -1.7187446}
      - {x: 1.0624946, y: -1.5}
      - {x: 1, y: -1.4999739}
      - {x: 0.9999865, y: -1.4687437}
      - {x: 0.56249464, y: -1.25}
      - {x: 0.5, y: -1.2499739}
      - {x: 0.4999865, y: -1.2187437}
      - {x: 0.062494602, y: -1}
      - {x: -0.0625045, y: -1.0000027}
      - {x: -0.5, y: -1.2187653}
      - {x: -0.5000261, y: -1.25}
      - {x: -0.5156313, y: -1.2500135}
      - {x: -0.53122663, y: -1.2812608}
      - {x: 0, y: -1.5468273}
      - {x: -0.0000135, y: -1.4687437}
      - {x: -0.3993104, y: -1.269039}
      - {x: 0.0000117, y: -1.0625054}
      - {x: 0.45313132, y: -1.296875}
      - {x: 0.5000054, y: -1.2968777}
      - {x: 1.5000054, y: -1.796875}
      - {x: 1.5468795, y: -1.7968724}
      - {x: 2.0000117, y: -1.5625054}
      - {x: 2.3993104, y: -1.7690948}
      - {x: 2, y: -1.9687653}
  m_VertexDistance: 0.0005
  m_OffsetDistance: 0.000005
--- !u!50 &1329646991
Rigidbody2D:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1329646985}
  m_BodyType: 2
  m_Simulated: 1
  m_UseFullKinematicContacts: 0
  m_UseAutoMass: 0
  m_Mass: 1
  m_LinearDrag: 0
  m_AngularDrag: 0.05
  m_GravityScale: 1
  m_Material: {fileID: 0}
  m_Interpolate: 0
  m_SleepingMode: 1
  m_CollisionDetection: 0
  m_Constraints: 0
--- !u!19719996 &1329646992
TilemapCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1329646985}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 1
  m_Offset: {x: 0, y: 0}
  m_MaximumTileChangeCount: 1000
  m_ExtrusionFactor: 0.00001
--- !u!1 &1372637015
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1372637016}
  - component: {fileID: 1372637017}
  m_Layer: 0
  m_Name: Trigger - Ground - Level 1 - Right
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1372637016
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1372637015}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.5, y: 0, z: 4}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 628296546}
  - {fileID: 907868450}
  m_Father: {fileID: 0}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &1372637017
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1372637015}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 21679eee47d160141b453fea1decf53a, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 1
--- !u!1001 &1533418801
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 319720608}
    m_Modifications:
    - target: {fileID: 5021506805927386240, guid: 2743e1476b242f44092e871840e6225b,
        type: 3}
      propertyPath: m_Name
      value: Rock_Purple_Small
      objectReference: {fileID: 0}
    - target: {fileID: 5021506805927386240, guid: 2743e1476b242f44092e871840e6225b,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5021506805927386241, guid: 2743e1476b242f44092e871840e6225b,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 5.36
      objectReference: {fileID: 0}
    - target: {fileID: 5021506805927386241, guid: 2743e1476b242f44092e871840e6225b,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.62
      objectReference: {fileID: 0}
    - target: {fileID: 5021506805927386241, guid: 2743e1476b242f44092e871840e6225b,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5021506805927386241, guid: 2743e1476b242f44092e871840e6225b,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5021506805927386241, guid: 2743e1476b242f44092e871840e6225b,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5021506805927386241, guid: 2743e1476b242f44092e871840e6225b,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5021506805927386241, guid: 2743e1476b242f44092e871840e6225b,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5021506805927386241, guid: 2743e1476b242f44092e871840e6225b,
        type: 3}
      propertyPath: m_RootOrder
      value: 23
      objectReference: {fileID: 0}
    - target: {fileID: 5021506805927386241, guid: 2743e1476b242f44092e871840e6225b,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5021506805927386241, guid: 2743e1476b242f44092e871840e6225b,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5021506805927386241, guid: 2743e1476b242f44092e871840e6225b,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5021506805927386242, guid: 2743e1476b242f44092e871840e6225b,
        type: 3}
      propertyPath: m_SortingLayerID
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5021506805927386242, guid: 2743e1476b242f44092e871840e6225b,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5021506805927386243, guid: 2743e1476b242f44092e871840e6225b,
        type: 3}
      propertyPath: m_SortingLayerID
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 2743e1476b242f44092e871840e6225b, type: 3}
--- !u!4 &1533418802 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5021506805927386241, guid: 2743e1476b242f44092e871840e6225b,
    type: 3}
  m_PrefabInstance: {fileID: 1533418801}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1782783679
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1782783680}
  - component: {fileID: 1782783682}
  - component: {fileID: 1782783681}
  m_Layer: 0
  m_Name: Tilemap - Ground - Base
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1782783680
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1782783679}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 968377523}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!483693784 &1782783681
TilemapRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1782783679}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_ChunkSize: {x: 32, y: 32, z: 32}
  m_ChunkCullingBounds: {x: 0, y: 0.484375, z: 0}
  m_MaxChunkCount: 16
  m_MaxFrameAge: 16
  m_SortOrder: 3
  m_Mode: 1
  m_DetectChunkCullingBounds: 0
  m_MaskInteraction: 0
--- !u!1839735485 &1782783682
Tilemap:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1782783679}
  m_Enabled: 1
  m_Tiles:
  - first: {x: -6, y: -10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 27
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: -10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 11
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 11
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 11
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 11
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 11
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 11
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 11
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 11
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 11
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 11
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: -10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 11
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: -10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 11
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -10, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 26
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -6, y: -9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: -9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: -9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: -9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -9, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 7
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -6, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -8, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 7
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -6, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -7, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 17
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -7, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 16
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -7, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 16
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -7, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 16
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -7, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 16
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -7, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 14
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -7, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 7
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -6, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -6, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 22
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -6, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -6, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -6, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 17
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -6, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -6, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 14
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -6, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 24
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -6, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 13
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -6, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 7
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -6, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -5, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 17
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -5, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 25
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -5, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -5, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -5, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 22
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -5, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -5, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 15
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -5, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 15
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -5, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 7
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -6, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -4, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 22
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -4, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -4, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -4, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -4, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 21
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -4, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -4, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 13
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -4, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 15
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 7
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -6, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -3, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 22
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -3, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -3, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -3, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -3, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -3, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 23
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -3, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 14
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 7
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -6, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -2, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 17
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -2, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 16
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -2, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 25
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -2, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -2, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -2, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 17
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -2, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 24
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -2, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 14
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -2, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 18
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -2, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 18
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -2, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 13
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 7
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -6, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: -1, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 22
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: -1, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: -1, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: -1, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 20
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -1, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 24
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: -1, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 22
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -1, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 13
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: -1, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 15
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 12
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: -1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 7
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -6, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: 0, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 22
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: 0, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: 0, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 17
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 0, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 0, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 16
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 0, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 10
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 0, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 16
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 7
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 0, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 15
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 0, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 24
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 0, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 13
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 0, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 7
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -6, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: 1, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 21
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: 1, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 18
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: 1, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 21
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 1, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 18
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 1, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 18
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 1, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 18
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 1, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 18
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 4
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 1, z: 2}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 13
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 1, z: 4}
    second:
      serializedVersion: 2
      m_TileIndex: 1
      m_TileSpriteIndex: 13
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 11
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 3
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 1, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 7
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -6, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 2, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 7
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -6, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 9
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 8
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 3, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 7
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -6, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 19
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -5, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -4, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -3, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -2, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: -1, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 0, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 1, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 2, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 3, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 4, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 5, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 6, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 5
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  - first: {x: 7, y: 4, z: 0}
    second:
      serializedVersion: 2
      m_TileIndex: 0
      m_TileSpriteIndex: 6
      m_TileMatrixIndex: 0
      m_TileColorIndex: 0
      m_TileObjectToInstantiateIndex: 65535
      m_AllTileFlags: 1073741824
  m_AnimatedTiles: {}
  m_TileAssetArray:
  - m_RefCount: 209
    m_Data: {fileID: 11400000, guid: 3152ed6d0cc59d54fa8ee0ea59412829, type: 2}
  - m_RefCount: 75
    m_Data: {fileID: 11400000, guid: cdb7575da9d10f346ad8b0a600400c28, type: 2}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  m_TileSpriteArray:
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 0
    m_Data: {fileID: 0}
  - m_RefCount: 1
    m_Data: {fileID: 21300000, guid: 60919206ec62ae74e92382a275b3e531, type: 3}
  - m_RefCount: 1
    m_Data: {fileID: 21300000, guid: fabb83d01aa016848a2741f371a1ccbd, type: 3}
  - m_RefCount: 13
    m_Data: {fileID: 21300000, guid: 40c74bdb314e60147be4706b27817189, type: 3}
  - m_RefCount: 1
    m_Data: {fileID: 21300000, guid: ff182ddc523cdc949978ddbb764a0fa3, type: 3}
  - m_RefCount: 14
    m_Data: {fileID: 21300000, guid: 8af0c5a1b4b46cd4e908b6e919853f60, type: 3}
  - m_RefCount: 147
    m_Data: {fileID: 21300000, guid: 4495010f0f608e84498564b6ae27cfff, type: 3}
  - m_RefCount: 14
    m_Data: {fileID: 21300000, guid: fb021a0855ad898469c732016f6e3b9e, type: 3}
  - m_RefCount: 22
    m_Data: {fileID: 21300000, guid: bf7ebbf343cee7c4b9c1a7c3f629ecfe, type: 3}
  - m_RefCount: 13
    m_Data: {fileID: 21300000, guid: 6df307357d7c3aa48980f416b482aef0, type: 3}
  - m_RefCount: 1
    m_Data: {fileID: 21300000, guid: 2ee43175fe272a24ba3ef1aa441cae17, type: 3}
  - m_RefCount: 7
    m_Data: {fileID: 21300000, guid: be4b70f86d38cbf41b09740e793d03a0, type: 3}
  - m_RefCount: 4
    m_Data: {fileID: 21300000, guid: 29ac072f835dc6945b5e1d0aa71dcc03, type: 3}
  - m_RefCount: 5
    m_Data: {fileID: 21300000, guid: 2eac3474fe5c2714788e1ecece163387, type: 3}
  - m_RefCount: 7
    m_Data: {fileID: 21300000, guid: 9b24163680c21a44fb6c969cfd3cb61e, type: 3}
  - m_RefCount: 6
    m_Data: {fileID: 21300000, guid: 9d9eaa5596fe0024187edb8f8035bd8a, type: 3}
  - m_RefCount: 7
    m_Data: {fileID: 21300000, guid: f5a21f39a3306ad4e8b7c99428ddb2d9, type: 3}
  - m_RefCount: 1
    m_Data: {fileID: 21300000, guid: 3997bbb2174dc6342bee8b8803a2ffe2, type: 3}
  - m_RefCount: 1
    m_Data: {fileID: 21300000, guid: f427ccb89e8fdb745b8f47ea8b3d9d0c, type: 3}
  - m_RefCount: 3
    m_Data: {fileID: 21300000, guid: 4bada23310046ca4bb98f9d8bdf17a15, type: 3}
  - m_RefCount: 7
    m_Data: {fileID: 21300000, guid: 9c68dc1b6ad638c4daad0b20385272c3, type: 3}
  - m_RefCount: 1
    m_Data: {fileID: 21300000, guid: 338493abd75ba2c48b8cb98205165bab, type: 3}
  - m_RefCount: 4
    m_Data: {fileID: 21300000, guid: 39f1c328a0d51dc4884a9e205c45d9aa, type: 3}
  - m_RefCount: 2
    m_Data: {fileID: 21300000, guid: 7fcc234f5b17dfc438a4b3af78c231c5, type: 3}
  - m_RefCount: 1
    m_Data: {fileID: 21300000, guid: 9d239763008adf64a957bc99534d948d, type: 3}
  - m_RefCount: 1
    m_Data: {fileID: 21300000, guid: 1fa9a4a9631823f44b33f6ac73e6e1d3, type: 3}
  m_TileMatrixArray:
  - m_RefCount: 284
    m_Data:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  m_TileColorArray:
  - m_RefCount: 284
    m_Data: {r: 1, g: 1, b: 1, a: 1}
  m_TileObjectToInstantiateArray: []
  m_AnimationFrameRate: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Origin: {x: -19, y: -17, z: 0}
  m_Size: {x: 36, y: 43, z: 4}
  m_TileAnchor: {x: 0, y: 0, z: 0}
  m_TileOrientation: 0
  m_TileOrientationMatrix:
    e00: 1
    e01: 0
    e02: 0
    e03: 0
    e10: 0
    e11: 1
    e12: 0
    e13: 0
    e20: 0
    e21: 0
    e22: 1
    e23: 0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
--- !u!1 &1818499532
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1818499533}
  - component: {fileID: 1818499534}
  m_Layer: 0
  m_Name: Trigger - Ground - Base - Left
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1818499533
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1818499532}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.5, y: -1.5, z: 2}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 382214883}
  - {fileID: 1127765711}
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &1818499534
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1818499532}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: dbfe26f852ac8d046afaba7b0663c9b0, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 1
--- !u!1001 &1894663897
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 319720608}
    m_Modifications:
    - target: {fileID: 7683452084394070184, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_SortingLayerID
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070184, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070198, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_Name
      value: Tree_Palm (2)
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070198, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.52
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.61
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 3b0788909d2477247a43cb4f4ceb765d, type: 3}
--- !u!4 &1894663898 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
    type: 3}
  m_PrefabInstance: {fileID: 1894663897}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &2048416127
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 319720608}
    m_Modifications:
    - target: {fileID: 7683452084394070184, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_SortingLayerID
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070184, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070198, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_Name
      value: Tree_Palm (1)
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070198, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 1.596
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.59
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 3b0788909d2477247a43cb4f4ceb765d, type: 3}
--- !u!4 &2048416128 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7683452084394070199, guid: 3b0788909d2477247a43cb4f4ceb765d,
    type: 3}
  m_PrefabInstance: {fileID: 2048416127}
  m_PrefabAsset: {fileID: 0}
--- !u!210 &931523779015212896
SortingGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2501001021266614073}
  m_Enabled: 1
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!114 &931523779015212897
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2501001021266614073}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ce0038be263d74d9b95eb7a5862c9f4e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  movementSpeed: 2
--- !u!1 &2501001021080404010
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2501001021080404011}
  m_Layer: 0
  m_Name: Witch
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2501001021080404011
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2501001021080404010}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.4375, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 2501001022354933747}
  m_Father: {fileID: 2501001021266614076}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2501001021266614073
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2501001021266614076}
  - component: {fileID: 2501001021266614074}
  - component: {fileID: 931523779015212896}
  - component: {fileID: 931523779015212897}
  m_Layer: 0
  m_Name: Player_Isometric_Witch
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!50 &2501001021266614074
Rigidbody2D:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2501001021266614073}
  m_BodyType: 0
  m_Simulated: 1
  m_UseFullKinematicContacts: 1
  m_UseAutoMass: 0
  m_Mass: 1
  m_LinearDrag: 0
  m_AngularDrag: 0.05
  m_GravityScale: 0
  m_Material: {fileID: 0}
  m_Interpolate: 0
  m_SleepingMode: 1
  m_CollisionDetection: 0
  m_Constraints: 4
--- !u!4 &2501001021266614076
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2501001021266614073}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.28, y: -1.51, z: 2}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 2501001022903169249}
  - {fileID: 2501001021080404011}
  m_Father: {fileID: 0}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2501001022354933746
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2501001022354933747}
  - component: {fileID: 2501001022354933750}
  - component: {fileID: 2501001022354933749}
  - component: {fileID: 2501001022354933748}
  m_Layer: 0
  m_Name: WitchRender
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2501001022354933747
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2501001022354933746}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2501001021080404011}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2501001022354933748
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2501001022354933746}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0631f36ac84554335b5b1f70f25312fb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!95 &2501001022354933749
Animator:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2501001022354933746}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 96400a5b745e640bbbdd72763b2beb38, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!212 &2501001022354933750
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2501001022354933746}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: ********************************, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.43, y: 0.7}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &2501001022903169248
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2501001022903169249}
  - component: {fileID: 2501001022903169250}
  m_Layer: 0
  m_Name: PlayerCollider
  m_TagString: Player
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 4294967295
  m_IsActive: 1
--- !u!4 &2501001022903169249
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2501001022903169248}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.011, y: 0.0725, z: 0}
  m_LocalScale: {x: 0.2, y: 0.2, z: 5}
  m_Children: []
  m_Father: {fileID: 2501001021266614076}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!58 &2501001022903169250
CircleCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2501001022903169248}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  serializedVersion: 2
  m_Radius: 0.5
--- !u!4 &3243268130976060149 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 3243268130976060149, guid: e1f468f225fe49042a71cdbf59d7616e,
    type: 3}
  m_PrefabInstance: {fileID: 5257767987461742341}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &5257767987461742341
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 319720608}
    m_Modifications:
    - target: {fileID: 3243268130976060149, guid: e1f468f225fe49042a71cdbf59d7616e,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 7.11
      objectReference: {fileID: 0}
    - target: {fileID: 3243268130976060149, guid: e1f468f225fe49042a71cdbf59d7616e,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.77
      objectReference: {fileID: 0}
    - target: {fileID: 3243268130976060149, guid: e1f468f225fe49042a71cdbf59d7616e,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3243268130976060149, guid: e1f468f225fe49042a71cdbf59d7616e,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3243268130976060149, guid: e1f468f225fe49042a71cdbf59d7616e,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3243268130976060149, guid: e1f468f225fe49042a71cdbf59d7616e,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3243268130976060149, guid: e1f468f225fe49042a71cdbf59d7616e,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3243268130976060149, guid: e1f468f225fe49042a71cdbf59d7616e,
        type: 3}
      propertyPath: m_RootOrder
      value: 7
      objectReference: {fileID: 0}
    - target: {fileID: 3243268130976060149, guid: e1f468f225fe49042a71cdbf59d7616e,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3243268130976060149, guid: e1f468f225fe49042a71cdbf59d7616e,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3243268130976060149, guid: e1f468f225fe49042a71cdbf59d7616e,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4373228545755455497, guid: e1f468f225fe49042a71cdbf59d7616e,
        type: 3}
      propertyPath: m_Name
      value: Bones_1
      objectReference: {fileID: 0}
    - target: {fileID: 4373228545755455497, guid: e1f468f225fe49042a71cdbf59d7616e,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4649703682565946028, guid: e1f468f225fe49042a71cdbf59d7616e,
        type: 3}
      propertyPath: m_SortingLayerID
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4649703682565946028, guid: e1f468f225fe49042a71cdbf59d7616e,
        type: 3}
      propertyPath: m_SortingLayer
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: e1f468f225fe49042a71cdbf59d7616e, type: 3}
