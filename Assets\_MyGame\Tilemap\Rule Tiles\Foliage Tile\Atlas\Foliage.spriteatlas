%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!687078895 &4343727234628468602
SpriteAtlas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Foliage
  m_EditorData:
    serializedVersion: 2
    textureSettings:
      serializedVersion: 2
      anisoLevel: 1
      compressionQuality: 50
      maxTextureSize: 2048
      textureCompression: 0
      filterMode: 1
      generateMipMaps: 0
      readable: 0
      crunchedCompression: 0
      sRGB: 0
    platformSettings:
    - serializedVersion: 3
      m_BuildTarget: DefaultTexturePlatform
      m_MaxTextureSize: 2048
      m_ResizeAlgorithm: 0
      m_TextureFormat: -1
      m_TextureCompression: 0
      m_CompressionQuality: 50
      m_CrunchedCompression: 0
      m_AllowsAlphaSplitting: 0
      m_Overridden: 0
      m_AndroidETC2FallbackOverride: 0
      m_ForceMaximumCompressionQuality_BC6H_BC7: 1
    packingSettings:
      serializedVersion: 2
      padding: 4
      blockOffset: 1
      allowAlphaSplitting: 0
      enableRotation: 1
      enableTightPacking: 1
      enableAlphaDilation: 0
    secondaryTextureSettings: {}
    variantMultiplier: 1
    packables:
    - {fileID: 2800000, guid: 978047fa06043f24db6c15d7cd535997, type: 3}
    bindAsDefault: 1
    isAtlasV2: 0
    cachedData: {fileID: 0}
  m_MasterAtlas: {fileID: 0}
  m_PackedSprites:
  - {fileID: 21300010, guid: 978047fa06043f24db6c15d7cd535997, type: 3}
  - {fileID: 21300008, guid: 978047fa06043f24db6c15d7cd535997, type: 3}
  - {fileID: 21300014, guid: 978047fa06043f24db6c15d7cd535997, type: 3}
  - {fileID: 21300012, guid: 978047fa06043f24db6c15d7cd535997, type: 3}
  - {fileID: 21300002, guid: 978047fa06043f24db6c15d7cd535997, type: 3}
  - {fileID: 21300000, guid: 978047fa06043f24db6c15d7cd535997, type: 3}
  - {fileID: 21300006, guid: 978047fa06043f24db6c15d7cd535997, type: 3}
  - {fileID: 21300004, guid: 978047fa06043f24db6c15d7cd535997, type: 3}
  m_PackedSpriteNamesToIndex:
  - foliage_5
  - foliage_4
  - foliage_7
  - foliage_6
  - foliage_1
  - foliage_0
  - foliage_3
  - foliage_2
  m_RenderDataMap: {}
  m_Tag: Foliage
  m_IsVariant: 0
