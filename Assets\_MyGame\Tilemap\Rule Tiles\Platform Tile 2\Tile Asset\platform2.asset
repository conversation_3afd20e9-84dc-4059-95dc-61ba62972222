%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9d1514134bc4fbd41bb739b1b9a49231, type: 3}
  m_Name: platform2
  m_EditorClassIdentifier: 
  m_DefaultSprite: {fileID: 21300020, guid: b206d8d9f3ab87f4aa256b0df86a7f29, type: 3}
  m_DefaultGameObject: {fileID: 0}
  m_DefaultColliderType: 1
  m_TilingRules:
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300000, guid: b206d8d9f3ab87f4aa256b0df86a7f29, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0200000002000000010000000100000001000000
    m_NeighborPositions:
    - {x: 0, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0, y: -1, z: 0}
    - {x: 1, y: -1, z: 0}
    m_RuleTransform: 2
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300004, guid: b206d8d9f3ab87f4aa256b0df86a7f29, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0200000001000000010000000100000001000000
    m_NeighborPositions:
    - {x: -1, y: 1, z: 0}
    - {x: 0, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0, y: -1, z: 0}
    m_RuleTransform: 2
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300014, guid: b206d8d9f3ab87f4aa256b0df86a7f29, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0100000001000000010000000200000001000000
    m_NeighborPositions:
    - {x: 0, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: -1, y: -1, z: 0}
    - {x: 0, y: -1, z: 0}
    m_RuleTransform: 2
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300006, guid: b206d8d9f3ab87f4aa256b0df86a7f29, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 01000000020000000100000001000000
    m_NeighborPositions:
    - {x: 0, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0, y: -1, z: 0}
    m_RuleTransform: 2
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300002, guid: b206d8d9f3ab87f4aa256b0df86a7f29, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 02000000010000000100000001000000
    m_NeighborPositions:
    - {x: 0, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0, y: -1, z: 0}
    m_RuleTransform: 0
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300008, guid: b206d8d9f3ab87f4aa256b0df86a7f29, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 01000000010000000100000001000000
    m_NeighborPositions:
    - {x: 0, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0, y: -1, z: 0}
    m_RuleTransform: 0
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300010, guid: b206d8d9f3ab87f4aa256b0df86a7f29, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 01000000020000000100000002000000
    m_NeighborPositions:
    - {x: 0, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0, y: -1, z: 0}
    m_RuleTransform: 2
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300012, guid: b206d8d9f3ab87f4aa256b0df86a7f29, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 01000000010000000100000002000000
    m_NeighborPositions:
    - {x: 0, y: 1, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0, y: -1, z: 0}
    m_RuleTransform: 0
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300016, guid: b206d8d9f3ab87f4aa256b0df86a7f29, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0200000001000000
    m_NeighborPositions:
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    m_RuleTransform: 2
  - m_Id: 0
    m_Sprites:
    - {fileID: 21300018, guid: b206d8d9f3ab87f4aa256b0df86a7f29, type: 3}
    m_GameObject: {fileID: 0}
    m_AnimationSpeed: 1
    m_PerlinScale: 0.5
    m_Output: 0
    m_ColliderType: 1
    m_RandomTransform: 0
    m_Neighbors: 0100000001000000
    m_NeighborPositions:
    - {x: -1, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    m_RuleTransform: 0
